# Build stage
FROM eclipse-temurin:23-jdk as maven-builder
# Install Maven
RUN apt-get update && apt-get install -y maven && apt-get clean
# Set working directory
WORKDIR /app
# Copy pom.xml first for better caching
COPY pom.xml .
# Copy source code
COPY src ./src
# Build the application with skipping tests
RUN mvn clean package -DskipTests
# Extract stage
FROM eclipse-temurin:23-jdk as extractor
# Install PostgreSQL client tools
RUN apt-get update && apt-get install -y postgresql-client && apt-get clean
# Create application directory
WORKDIR /app
# Copy the JAR from maven-builder stage
COPY --from=maven-builder /app/target/*.jar app.jar
# Extract the JAR using Spring Boot's built-in capability
RUN java -Djarmode=layertools -jar app.jar extract && rm app.jar
# Production stage
FROM eclipse-temurin:23-jdk
# Install PostgreSQL client tools
RUN apt-get update && apt-get install -y postgresql-client && apt-get clean
# Create application directory
WORKDIR /app
# Copy the extracted layers from extractor stage
COPY --from=extractor /app/dependencies/ ./
COPY --from=extractor /app/spring-boot-loader/ ./
COPY --from=extractor /app/snapshot-dependencies/ ./
COPY --from=extractor /app/application/ ./
# Set the entrypoint to run the application
ENTRYPOINT ["java", "org.springframework.boot.loader.launch.JarLauncher"]
