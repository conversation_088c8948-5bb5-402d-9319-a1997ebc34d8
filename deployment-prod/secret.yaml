apiVersion: v1
kind: Secret
metadata:
  namespace: evdi-production-one-click-deployment
  name: harbor-registry-secret
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: "********************************************************************************************************************************************************************"

---
apiVersion: v1
kind: Secret
metadata:
  name: one-click-deployer-secret
  namespace: evdi-production-one-click-deployment
  labels:
    app: one-click-deployer
stringData:
  CF_AUTH_EMAIL: "<EMAIL>"
  CF_API_TOKEN: "ArcQFu6AlGFE8h6TDsCLoELO_9jtAspmecoQnuiA"
  STRIPE_API_KEY: "sk_test_51RWxg0FNlboFfRX8ZLC5fHcW7MhJBJWHXM7TIBRpkSHVXoBrQKilVQHFUBVUBfRAAbzRS6kWwq49geGeAgI4617800yvrUB7vj"
  STRIPE_WEBHOOK_SECRET: "whsec_dMDQekxL5XdErwBb2Ya3EyD7KIzYXlhu"
  STRIPE_SUCCESS_URL: "https://oneclick-stg.empe.io/"
  STRIPE_CANCEL_URL: "https://oneclick-stg.empe.io/"
  FAUCET_URL: "http://internal-faucet/faucet/address"
  DB_URL: "***********************************************************************"
  DB_USERNAME: "postgres"
  DB_PASSWORD: "lfdsncdfcseirtvshlufrsiuhbdfjdfhbkdsfgiuey7r8dfhdsbjsehdjb"
  BACKUP_S3_BUCKET: "evdi-backup"
  BACKUP_S3_REGION: "waw"
  BACKUP_S3_ENDPOINT: "https://s3.waw.io.cloud.ovh.net/"
  BACKUP_S3_ACCESS_KEY: "3ecc01f03cc445c69b402490d2aa9e60"
  BACKUP_S3_SECRET_KEY: "eae183a7242b4f93945f2ca2376a92d9"
  MAILERSEND_API_TOKEN: "mlsn.1f5aa6033f49ebced999cd0ae28102d9b62b1019b172c71b06fde7b8854bf916"
  ISSUER_ADMIN_SECRET: "7bpyalJbMVSe4T0kltmXdkyej2hUVIYC"