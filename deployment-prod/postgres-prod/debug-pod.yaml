apiVersion: v1
kind: Pod
metadata:
  name: debug-pod
  namespace: evdi-production-one-click-deployment
spec:
  containers:
    - name: debug-container
      image: busybox
      command: [ "/bin/sh", "-c", "sleep 3600" ]
      volumeMounts:
        - mountPath: /backup/pgdump
          name: backup-volume
  volumes:
    - name: backup-volume
      persistentVolumeClaim:
        claimName: evdi-production-postgresql-pgdumpall
  restartPolicy: Never