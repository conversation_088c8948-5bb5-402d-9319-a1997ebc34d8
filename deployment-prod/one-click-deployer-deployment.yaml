apiVersion: apps/v1
kind: Deployment
metadata:
  name: one-click-deployer
  namespace: evdi-production-one-click-deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      app: one-click-deployer
  template:
    metadata:
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/path: "/actuator/prometheus"
        prometheus.io/port: "8080"
      labels:
        app: one-click-deployer
    spec:
      containers:
        - name: one-click-deployer
          image: 309596z9.c1.gra9.container-registry.ovh.net/empe/services/one-click-deployer:0.0.56
          imagePullPolicy: Always
          ports:
            - containerPort: 9091
          env:
            - name: SERVER_PORT
              value: "9091"
            - name: DB_URL
              valueFrom:
                secretKeyRef:
                  name: one-click-deployer-secret
                  key: DB_URL
            - name: DB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: one-click-deployer-secret
                  key: DB_USERNAME
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: one-click-deployer-secret
                  key: DB_PASSWORD
            - name: DOMAIN
              value: "evdi.app"
            - name: ISSUER_SUFFIX
              value: "-issuer"
            - name: VERIFIER_SUFFIX
              value: "-verifier"
            - name: CF_AUTH_EMAIL
              valueFrom:
                secretKeyRef:
                  name: one-click-deployer-secret
                  key: CF_AUTH_EMAIL
            - name: CF_API_TOKEN
              valueFrom:
                secretKeyRef:
                  name: one-click-deployer-secret
                  key: CF_API_TOKEN
            - name: STRIPE_API_KEY
              valueFrom:
                secretKeyRef:
                  name: one-click-deployer-secret
                  key: STRIPE_API_KEY
            - name: STRIPE_WEBHOOK_SECRET
              valueFrom:
                secretKeyRef:
                  name: one-click-deployer-secret
                  key: STRIPE_WEBHOOK_SECRET
            - name: FAUCET_URL
              valueFrom:
                secretKeyRef:
                  name: one-click-deployer-secret
                  key: FAUCET_URL
            - name: BACKUP_S3_BUCKET
              valueFrom:
                secretKeyRef:
                  name: one-click-deployer-secret
                  key: BACKUP_S3_BUCKET
            - name: BACKUP_S3_REGION
              valueFrom:
                secretKeyRef:
                  name: one-click-deployer-secret
                  key: BACKUP_S3_REGION
            - name: ISSUER_ADMIN_SECRET
              valueFrom:
                secretKeyRef:
                  name: one-click-deployer-secret
                  key: ISSUER_ADMIN_SECRET
            - name: BACKUP_S3_ENDPOINT
              valueFrom:
                secretKeyRef:
                  name: one-click-deployer-secret
                  key: BACKUP_S3_ENDPOINT
            - name: BACKUP_S3_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: one-click-deployer-secret
                  key: BACKUP_S3_ACCESS_KEY
            - name: BACKUP_S3_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: one-click-deployer-secret
                  key: BACKUP_S3_SECRET_KEY
            - name: MAILERSEND_API_TOKEN
              valueFrom:
                secretKeyRef:
                  name: one-click-deployer-secret
                  key: MAILERSEND_API_TOKEN
            - name: MAILERSEND_FROM_EMAIL
              value: "<EMAIL>"
            - name: MAILERSEND_FROM_NAME
              value: "One Click Deployer"
            - name: MAILERSEND_TEMPLATE_EMAIL_CONFIRMATION_ID
              value: "vywj2lpz19kg7oqz"
            - name: MAILERSEND_TEMPLATE_RESET_PASSWORD_ID
              value: "0r83ql3jq8vgzw1j"
            - name: MAILERSEND_TEMPLATE_RESET_PASSWORD_URL
              value: "https://oneclick.empe.io/reset-password"
            - name: MAILERSEND_TEMPLATE_RESET_CONFIRMATION_URL
              value: "https://oneclick.empe.io/confirm-email"
            - name: TOKEN_RETRY_LIMIT
              value: "3"
            - name: TOKEN_COOLDOWN_MINUTES
              value: "5"
            - name: K8S_CONFIG_PATH
              value: "/etc/kube/config"
            - name: K8S_NAMESPACE
              value: "evdi-production-one-click-deployment"
            - name: BACKUP_DIRECTORY
              value: "/tmp/backups"
            - name: ISTIO_IP
              value: "*************"
            - name: TOKEN_UP_CRON_INTERVAL
              value: 0 0/1 * * * *
          readinessProbe:
            httpGet:
              path: /actuator/health/readiness
              port: 9091
            initialDelaySeconds: 40
            periodSeconds: 5
          livenessProbe:
            httpGet:
              path: /actuator/health/liveness
              port: 9091
            initialDelaySeconds: 40
            periodSeconds: 5
          volumeMounts:
            - name: kubeconfig-volume
              mountPath: /etc/kube/config
              subPath: config
              readOnly: true
            - name: backup-volume
              mountPath: /tmp/backups
      imagePullSecrets:
        - name: harbor-registry-secret
      volumes:
        - name: kubeconfig-volume
          secret:
            secretName: kubeconfig-secret
        - name: backup-volume
          emptyDir: {}