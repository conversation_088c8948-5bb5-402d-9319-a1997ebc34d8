apiVersion: apps/v1
kind: Deployment
metadata:
  name: one-click-deployer
  namespace: customer-issuer-verifier
spec:
  replicas: 1
  selector:
    matchLabels:
      app: one-click-deployer
  template:
    metadata:
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/path: "/actuator/prometheus"
        prometheus.io/port: "8080"
      labels:
        app: one-click-deployer
    spec:
      containers:
        - name: one-click-deployer
          image: 309596z9.c1.gra9.container-registry.ovh.net/empe/services/one-click-deployer:0.0.6
          imagePullPolicy: Always
          ports:
            - containerPort: 9091
          env:
            - name: SERVER_PORT
              value: "9091"
            - name: DB_URL
              value: "************************************************************************************************************"
            - name: DB_USERNAME
              value: "one_click_deployer_user"
            - name: DB_PASSWORD
              value: "one_click_deployer_user_password"
            - name: CF_AUTH_EMAIL
              valueFrom:
                secretKeyRef:
                  name: cloudflare-secret
                  key: authEmail
            - name: CF_API_TOKEN
              valueFrom:
                secretKeyRef:
                  name: cloudflare-secret
                  key: apiToken
            - name: FAUCET_URL
              value: "http://internal-faucet/faucet/address"
            - name: MAILERSEND_API_TOKEN
              valueFrom:
                secretKeyRef:
                  name: one-click-deployer-secret
                  key: MAILERSEND_API_TOKEN
            - name: MAILERSEND_FROM_EMAIL
              value: "<EMAIL>"
            - name: MAILERSEND_FROM_NAME
              value: "One Click Deployer"
            - name: MAILERSEND_TEMPLATE_EMAIL_CONFIRMATION_ID
              value: "vywj2lpz19kg7oqz"
            - name: MAILERSEND_TEMPLATE_RESET_PASSWORD_ID
              value: "0r83ql3jq8vgzw1j"
            - name: MAILERSEND_TEMPLATE_RESET_PASSWORD_URL
              value: "https://oneclick.empe.io/reset-password"
            - name: MAILERSEND_TEMPLATE_RESET_CONFIRMATION_URL
              value: "https://oneclick.empe.io/confirm-email"
            - name: TOKEN_RETRY_LIMIT
              value: "3"
            - name: TOKEN_COOLDOWN_MINUTES
              value: "5"
            - name: K8S_CONFIG_PATH
              value: "/etc/kube/config"
          readinessProbe:
            httpGet:
              path: /actuator/health/readiness
              port: 9091
            initialDelaySeconds: 10
            periodSeconds: 5
          livenessProbe:
            httpGet:
              path: /actuator/health/liveness
              port: 9091
            initialDelaySeconds: 10
            periodSeconds: 5
          volumeMounts:
            - name: kubeconfig-volume
              mountPath: /etc/kube/config
              subPath: config
              readOnly: true
      imagePullSecrets:
        - name: harbor-registry-secret
      volumes:
        - name: kubeconfig-volume
          secret:
            secretName: kubeconfig-secret