apiVersion: v1
kind: Pod
metadata:
  name: debug-pod
  namespace: telegram-game
spec:
  containers:
    - name: debug-container
      image: busybox
      command: [ "/bin/sh", "-c", "sleep 3600" ]
      volumeMounts:
        - mountPath: /backup/pgdump
          name: backup-volume
  volumes:
    - name: backup-volume
      persistentVolumeClaim:
        claimName: postgresql-testnet-pgdumpall
  restartPolicy: Never