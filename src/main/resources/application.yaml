management:
  endpoint:
    health:
      show-details: "ALWAYS"
      probes:
        enabled: true
  endpoints:
    enabled-by-default: true
    web:
      exposure:
        include: metrics, health, caches, restart, prometheus
server:
  port: ${SERVER_PORT:9091}

spring:
  application:
    name: one-click-deployer

  datasource:
    url: ${DB_URL:******************************************************}
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:postgres}
    driver-class-name: org.postgresql.Driver

  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false

  liquibase:
    change-log: classpath:changelog/changeLog-master.xml

mailersend:
  api:
    token: ${MAILERSEND_API_TOKEN:}
  from:
    email: ${MAILERSEND_FROM_EMAIL:}
    name: ${MAILERSEND_FROM_NAME:One Click Deployer}
  template:
    email-confirmation-id: ${MAILERSEND_TEMPLATE_EMAIL_CONFIRMATION_ID:}
    reset-password-id: ${MAILERSEND_TEMPLATE_RESET_PASSWORD_ID:}
    reset-password-url: ${MAILERSEND_TEMPLATE_RESET_PASSWORD_URL:http://localhost:9091/reset-password}
    email-confirmation-url: ${MAILERSEND_TEMPLATE_RESET_CONFIRMATION_URL:http://localhost:9091/reset-confirmation}

application:
  token:
    retry-limit: ${TOKEN_RETRY_LIMIT:5}
    cooldown: ${TOKEN_COOLDOWN_MINUTES:5}

issuer:
  admin-secret: ${ISSUER_ADMIN_SECRET:7bpyalJbMVSe4T0kltmXdkyej2hUVIYC}

kubernetes:
  config:
    path: ${K8S_CONFIG_PATH:/home/<USER>/.kube/config}

cloudflare:
  authEmail: ${CF_AUTH_EMAIL}
  apiToken: ${CF_API_TOKEN}

one-click:
  domain: ${APP_DOMAIN:evdi.app}
  issuerSuffix: ${ISSUER_SUFFIX:-issuer}
  verifierSuffix: ${VERIFIER_SUFFIX:-verifier}
  k8s-namespace: ${K8S_NAMESPACE:cloud-evdi-stg}
  istio-ip: ${ISTIO_IP:*************}

faucet:
  url: ${FAUCET_URL:http://localhost:64576/faucet/address}

tokenup:
    cronInterval: ${TOKEN_UP_CRON_INTERVAL:0 0/1 * * * *}

stripe:
  api-key: ${STRIPE_API_KEY}
  webhook-secret: ${STRIPE_WEBHOOK_SECRET}

graylog:
  url: "https://gra1.logs.ovh.com"
  token: "1bp42hck9n8ft9c0hqh8s092627v4geagu8t9h3bsd0t0pv1ul7"

backup:
  directory: ${BACKUP_DIRECTORY:/tmp/backups}
  s3:
    bucket: ${BACKUP_S3_BUCKET}
    region: ${BACKUP_S3_REGION}
    endpoint: ${BACKUP_S3_ENDPOINT}
    accessKey: ${BACKUP_S3_ACCESS_KEY}
    secretKey: ${BACKUP_S3_SECRET_KEY}

vault:
  addr: ${VAULT_ADDR:https://vault-stg.evdi.app}
  token: ${VAULT_TOKEN:hvs.z4dlinNnLMqj9g1CJFIz8irg}
