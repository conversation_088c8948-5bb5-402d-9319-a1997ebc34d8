<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.0.xsd">

    <!-- ChangeSet to create issuer_version table -->
    <changeSet id="create-issuer-version-table" author="pablo">
        <createTable tableName="issuer_version">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="label" type="varchar(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="version" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="chart_version" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>

</databaseChangeLog>
