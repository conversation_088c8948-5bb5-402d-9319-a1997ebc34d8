<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="
      http://www.liquibase.org/xml/ns/dbchangelog
      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.0.xsd">

    <!-- 3) create the new metadata table -->
    <changeSet id="3" author="pablo">

        <!-- 3.1  table definition -->
        <createTable tableName="metadata">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false" unique="true"/>
            </column>

            <!-- Enum value stored as String -->
            <column name="type" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>

            <column name="value" type="INT">
                <constraints nullable="false"/>
            </column>

            <!-- Many-to-one FK to user_subscription -->
            <column name="subscription_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <!-- 3.2  foreign-key constraints -->
        <addForeignKeyConstraint
                baseTableName="metadata"
                baseColumnNames="subscription_id"
                constraintName="fk_metadata_subscription"
                referencedTableName="user_subscription"
                referencedColumnNames="id"
                onDelete="CASCADE"/>

        <!-- 3.3  helpful indexes for fast joins -->
        <createIndex tableName="metadata" indexName="idx_metadata_subscription">
            <column name="subscription_id"/>
        </createIndex>

        <createIndex tableName="metadata" indexName="idx_metadata_type">
            <column name="type"/>
        </createIndex>

        <!-- 3.4  (optional) enforce one row per TYPE & owner -->
        <addUniqueConstraint
                tableName="metadata"
                columnNames="subscription_id,type"
                constraintName="uk_metadata_subscription_type"/>

    </changeSet>
</databaseChangeLog>