<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.0.xsd">

    <!-- ChangeSet to add organization and issuer_version_id to issuer table -->
    <changeSet id="add-organization-and-issuer-version-to-issuer" author="pablo">
        <addColumn tableName="issuer_deployment">
            <column name="organization" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="issuer_version_id" type="bigint">
                <constraints nullable="false"/>
            </column>
        </addColumn>

        <!-- Add foreign key constraint for issuer_version_id -->
        <addForeignKeyConstraint
                baseTableName="issuer_deployment"
                baseColumnNames="issuer_version_id"
                constraintName="fk_issuer_issuer_version"
                referencedTableName="issuer_version"
                referencedColumnNames="id"/>
    </changeSet>

</databaseChangeLog>
