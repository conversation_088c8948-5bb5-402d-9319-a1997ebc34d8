<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.0.xsd">

    <changeSet id="update-issuer-version-table-label-nullable" author="pablo">
        <modifyDataType tableName="verifier_version" columnName="label" newDataType="VARCHAR(255)"/>
        <dropNotNullConstraint tableName="verifier_version" columnName="label" columnDataType="VARCHAR(255)"/>
        <addUniqueConstraint tableName="verifier_version" columnNames="label"
                             constraintName="uq_verifier_version_label"/>
    </changeSet>

</databaseChangeLog>
