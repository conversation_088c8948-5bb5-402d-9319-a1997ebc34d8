<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
    http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="create-blockchain-configuration" author="cascade-ai">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="blockchain_configuration"/>
            </not>
        </preConditions>
        <createTable tableName="blockchain_configuration">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="rpc" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="lcd" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="faucet_url" type="VARCHAR(255)"/>
            <column name="type" type="VARCHAR(32)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

</databaseChangeLog>
