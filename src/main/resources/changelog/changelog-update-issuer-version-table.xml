<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.0.xsd">

    <changeSet id="update-issuer-version-table-label-nullable" author="pablo">
        <modifyDataType tableName="issuer_version" columnName="label" newDataType="VARCHAR(255)"/>
        <dropNotNullConstraint tableName="issuer_version" columnName="label" columnDataType="VARCHAR(255)"/>
        <addUniqueConstraint tableName="issuer_version" columnNames="label" constraintName="uq_issuer_version_label"/>
    </changeSet>

</databaseChangeLog>
