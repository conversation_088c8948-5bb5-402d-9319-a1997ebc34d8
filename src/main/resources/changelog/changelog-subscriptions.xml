<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="
          http://www.liquibase.org/xml/ns/dbchangelog
          http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.0.xsd">

    <!-- 2) create the new user_subscription table -->
    <changeSet id="2" author="pablo">
        <createTable tableName="user_subscription">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false" unique="true"/>
            </column>
            <column name="product_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP"
                    defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="current_period_end" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="stripe_subscription_id" type="VARCHAR(255)"/>
            <column name="subscription_active" type="BOOLEAN"
                    defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="UUID">
                <constraints nullable="false"/>
            </column>
            <column name="stripe_customer" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addForeignKeyConstraint
                baseTableName="user_subscription"
                baseColumnNames="user_id"
                constraintName="fk_user_subscription_user"
                referencedTableName="users"
                referencedColumnNames="id"/>

        <createIndex indexName="idx_user_subscription_user_id"
                     tableName="user_subscription">
            <column name="user_id"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>