<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.0.xsd">

    <changeSet id="1" author="pablo">
        <createTable tableName="refresh_token">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="token" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="expiry_date" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="UUID">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addForeignKeyConstraint baseTableName="refresh_token"
                                 baseColumnNames="user_id"
                                 referencedTableName="users"
                                 referencedColumnNames="id"
                                 constraintName="fk_refresh_token_user"/>
    </changeSet>
</databaseChangeLog>
