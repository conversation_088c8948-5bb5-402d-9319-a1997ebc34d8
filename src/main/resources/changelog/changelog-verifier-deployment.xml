<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.0.xsd">

    <!-- ChangeSet for creating verifier_deployment table -->
    <changeSet id="1" author="pablo">
        <createTable tableName="verifier_deployment">
            <column name="id"
                    type="UUID"
                    defaultValueComputed="gen_random_uuid()">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="verifier_name" type="varchar(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="full_host" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="deployment_type" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="UUID">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <!-- Add foreign key constraint for user_id -->
        <addForeignKeyConstraint
                baseTableName="verifier_deployment"
                baseColumnNames="user_id"
                constraintName="fk_verifier_deployment_user"
                referencedTableName="users"
                referencedColumnNames="id"/>
    </changeSet>

</databaseChangeLog>
