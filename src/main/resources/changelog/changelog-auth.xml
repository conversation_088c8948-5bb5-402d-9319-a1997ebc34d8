<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="20250626-1" author="denerev">
        <addColumn tableName="users">
            <column name="email_confirmed" type="boolean" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet id="20250626-2" author="denerev">
        <createTable tableName="token">
            <column name="id"
                    type="UUID"
                    defaultValueComputed="gen_random_uuid()">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="token" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="UUID">
                <constraints nullable="false" foreignKeyName="fk_token_user" referencedTableName="users"
                             referencedColumnNames="id"/>
            </column>
            <column name="expiry_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="creation_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="retry_count" type="int" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="20250626-3" author="denerev">
        <addUniqueConstraint tableName="token" columnNames="user_id, type" constraintName="uq_token_user_id_type"/>
    </changeSet>

</databaseChangeLog>
