<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
    http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="add-blockchain-configuration-to-issuerdeployment" author="cascade-ai">
        <addColumn tableName="issuer_deployment">
            <column name="blockchain_configuration_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        <addForeignKeyConstraint baseTableName="issuer_deployment"
                                 baseColumnNames="blockchain_configuration_id"
                                 referencedTableName="blockchain_configuration"
                                 referencedColumnNames="id"
                                 constraintName="fk_issuerdeployment_blockchainconfiguration"/>
    </changeSet>

</databaseChangeLog>
