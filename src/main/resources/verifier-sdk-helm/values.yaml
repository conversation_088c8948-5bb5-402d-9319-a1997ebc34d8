replicaCount: 1

uniqueName: "verifier-sdk"

image:
  repository: 309596z9.c1.gra9.container-registry.ovh.net/empe/services/verifier-sdk
  tag: "0.0.2"
  pullPolicy: Always

service:
  type: ClusterIP
  port: 80
  targetPort: 9004

persistence:
  enabled: true
  storageClass: csi-cinder-classic
  accessModes:
    - ReadWriteOnce
  size: 1Gi

env:
  HOST: http://127.0.0.1
  PORT: 9004
  DB_PATH: ./src/db
  CLIENT_SECRET: rDJwNmmEFyXK1QGv5mfkE8moCSgtxBSD
  # PostgreSQL Database Configuration
  DB_HOST: localhost
  DB_PORT: 5432
  DB_USERNAME: postgres
  DB_PASSWORD: postgres
  DB_DATABASE: issuer-service
  BLOCKCHAIN_RPC_URL: https://rpc-testnet.empe.io
  BLOCKCHAIN_REST_API_URL_LCD: https://lcd-testnet.empe.io
  BLOCKCHAIN_DENOM: uempe
  BLOCKCHAIN_FEE_AMOUNT: 20
  BLOCKCHAIN_GAS_AMOUNT: 200000
  BLOCKCHAIN_PREFIX: empe
  BLOCKCHAIN_FAUCET_URL: https://faucet-testnet.empe.io
  NETWORK: testnet

virtualService:
  gateway: default/public-gateway
  hosts:
    - verifier-sdk.empe.io

imagePullSecrets:
  - name: harbor-registry-secret
