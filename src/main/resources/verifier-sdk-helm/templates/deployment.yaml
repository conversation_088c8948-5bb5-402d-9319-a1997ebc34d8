apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.uniqueName }}
spec:
  strategy:
    type: Recreate
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: {{ .Values.uniqueName }}
  template:
    metadata:
      labels:
        app: {{ .Values.uniqueName }}
    spec:
      containers:
        - name: verifier-sdk
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - containerPort: {{ .Values.service.targetPort }}
          env:
            - name: HOST
              value: "{{ .Values.env.HOST }}"
            - name: NETWORK
              value: "{{ .Values.env.NETWORK }}"
            - name: BLOCKCHAIN_RPC_URL
              value: "{{ .Values.env.BLOCKCHAIN_RPC_URL }}"
            - name: PORT
              value: "{{ .Values.env.PORT }}"
            - name: DB_PATH
              value: "{{ .Values.env.DB_PATH }}"
            - name: CLIENT_SECRET
              value: "{{ .Values.env.CLIENT_SECRET }}"
            # PostgreSQL Database Configuration
            - name: DB_HOST
              value: "{{ .Values.env.DB_HOST }}"
            - name: DB_PORT
              value: "{{ .Values.env.DB_PORT }}"
            - name: DB_USERNAME
              value: "{{ .Values.env.DB_USERNAME }}"
            - name: DB_PASSWORD
              value: "{{ .Values.env.DB_PASSWORD }}"
            - name: DB_DATABASE
              value: "{{ .Values.env.DB_DATABASE }}"
      imagePullSecrets:
        {{ toYaml .Values.imagePullSecrets | nindent 8 }}