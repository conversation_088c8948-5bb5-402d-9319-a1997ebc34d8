apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: {{ .Values.uniqueName }}-virtualservice
spec:
  gateways:
    - {{ .Values.virtualService.gateway }}
  hosts:
    {{  toYaml .Values.virtualService.hosts | nindent 2 }}
  http:
    - route:
        - destination:
            host: {{ .Values.uniqueName }}.{{ .Release.Namespace }}.svc.cluster.local
            port:
              number: {{ .Values.service.port }}

