apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.uniqueName }}
spec:
  strategy:
    type: Recreate
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: {{ .Values.uniqueName }}
  template:
    metadata:
      labels:
        app: {{ .Values.uniqueName }}
    spec:
      containers:
        - name: issuer-sdk
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - containerPort: {{ .Values.service.targetPort }}
          env:
            - name: HOST
              value: "{{ .Values.env.HOST }}"
            - name: PORT
              value: "{{ .Values.env.PORT }}"
            - name: DB_PATH
              value: "{{ .Values.env.DB_PATH }}"
            - name: NETWORK
              value: "{{ .Values.env.NETWORK }}"
            - name: JWT_SECRET
              value: "{{ .Values.env.JWT_SECRET }}"
            - name: JWT_EXPIRES_IN
              value: "{{ .Values.env.JWT_EXPIRES_IN }}"
            - name: AUTHORIZATION_REQUEST_EXPIRES_IN
              value: "{{ .Values.env.AUTHORIZATION_REQUEST_EXPIRES_IN }}"
            - name: NAME
              value: "{{ .Values.env.NAME }}"
            - name: BLOCKCHAIN_PREFIX
              value: "{{ .Values.env.BLOCKCHAIN_PREFIX }}"
            - name: BLOCKCHAIN_RPC_URL
              value: "{{ .Values.env.BLOCKCHAIN_RPC_URL }}"
            - name: BLOCKCHAIN_REST_API_URL
              value: "{{ .Values.env.BLOCKCHAIN_REST_API_URL }}"
            - name: BLOCKCHAIN_DENOM
              value: "{{ .Values.env.BLOCKCHAIN_DENOM }}"
            - name: BLOCKCHAIN_FEE_AMOUNT
              value: "{{ .Values.env.BLOCKCHAIN_FEE_AMOUNT }}"
            - name: BLOCKCHAIN_GAS_AMOUNT
              value: "{{ .Values.env.BLOCKCHAIN_GAS_AMOUNT }}"
            - name: CLIENT_SECRET
              value: "{{ .Values.env.CLIENT_SECRET }}"
            # PostgreSQL Database Configuration
            - name: DB_HOST
              value: "{{ .Values.env.DB_HOST }}"
            - name: DB_PORT
              value: "{{ .Values.env.DB_PORT }}"
            - name: DB_USERNAME
              value: "{{ .Values.env.DB_USERNAME }}"
            - name: DB_PASSWORD
              value: "{{ .Values.env.DB_PASSWORD }}"
            - name: DB_DATABASE
              value: "{{ .Values.env.DB_DATABASE }}"
            # Vault Configuration
{{/*            - name: VAULT_ADDR*/}}
{{/*              value: "{{ .Values.env.VAULT_ADDR }}"*/}}
{{/*            - name: VAULT_USERNAME*/}}
{{/*              value: "{{ .Values.uniqueName }}"*/}}
{{/*            - name: VAULT_PASSWORD*/}}
{{/*              value: "{{ .Values.env.VAULT_PASSWORD }}"*/}}
            - name: ADMIN_SECRET
              value:  "{{ .Values.env.ADMIN_SECRET }}"
          resources:
            {{  toYaml .Values.resources | nindent 12 }}
      imagePullSecrets:
        {{  toYaml .Values.imagePullSecrets | nindent 8 }}