replicaCount: 1

uniqueName: "issuer-sdk-first-client"

image:
  repository: 309596z9.c1.gra9.container-registry.ovh.net/empe/services/issuer-sdk
  tag: "0.0.3"
  pullPolicy: Always

service:
  type: ClusterIP
  port: 80
  targetPort: 9001

persistence:
  enabled: true
  storageClass: csi-cinder-classic
  accessModes:
    - ReadWriteOnce
  size: 1Gi

env:
  HOST: http://127.0.0.1
  PORT: 9002
  DB_PATH: ./src/db
  NETWORK: testnet
  JWT_SECRET: 13hPKKN_ojejR3!tCTnft@@&G2q2iL51
  JWT_EXPIRES_IN: 3600
  AUTHORIZATION_REQUEST_EXPIRES_IN: 300
  NAME: Empe
  BLOCKCHAIN_PREFIX: empe
  BLOCKCHAIN_RPC_URL: https://rpc-testnet.empe.io
  BLOCKCHAIN_REST_API_URL: https://lcd-testnet.empe.io
  BLOCKCHAIN_DENOM: uempe
  BLOCKCHAIN_FEE_AMOUNT: 20
  BLOCKCHAIN_GAS_AMOUNT: 200000
  CLIENT_SECRET: rDJwNmmEFyXK1QGv5mfkE8moCSgtxBSD
  # PostgreSQL Database Configuration
  DB_HOST: localhost
  DB_PORT: 5432
  DB_USERNAME: postgres
  DB_PASSWORD: postgres
  DB_DATABASE: issuer-service
  # Vault Configuration
#  VAULT_ADDR: https://vault-stg.evdi.app
#  VAULT_USERNAME: ""
#  VAULT_PASSWORD: ""
  ADMIN_SECRET: 13hPKKN_ojejR3!tCTnft@@&G2q2iL51

virtualService:
  gateway: default/evdi-gateway
  hosts:
    - issuer-sdk.evdi.app

imagePullSecrets:
  - name: harbor-registry-secret
