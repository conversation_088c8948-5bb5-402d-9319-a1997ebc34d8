package io.empe.one_click_deployer.event;

import io.empe.one_click_deployer.entity.VerifierDeployment;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class VerifierDeploymentDeletedEvent extends ApplicationEvent {
    private final VerifierDeployment verifierDeployment;

    public VerifierDeploymentDeletedEvent(Object source, VerifierDeployment verifierDeployment) {
        super(source);
        this.verifierDeployment = verifierDeployment;
    }

}
