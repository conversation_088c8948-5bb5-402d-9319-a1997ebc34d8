package io.empe.one_click_deployer.event;

import io.empe.one_click_deployer.entity.VerifierDeployment;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class VerifierDeploymentSecretUpgradeEvent extends ApplicationEvent {
    private final VerifierDeployment verifierDeployment;
    private final String authKey;

    public VerifierDeploymentSecretUpgradeEvent(Object source, VerifierDeployment verifierDeployment, String authKey) {
        super(source);
        this.verifierDeployment = verifierDeployment;
        this.authKey = authKey;
    }
}
