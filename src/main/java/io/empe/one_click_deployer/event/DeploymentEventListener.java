package io.empe.one_click_deployer.event;

import io.empe.one_click_deployer.entity.IssuerDeployment;
import io.empe.one_click_deployer.entity.Status;
import io.empe.one_click_deployer.entity.VerifierDeployment;
import io.empe.one_click_deployer.service.deployment.DeploymentScheduler;
import io.empe.one_click_deployer.service.rest.impl.DeploymentSchedulerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

@Component
public class DeploymentEventListener {

    private final DeploymentSchedulerFactory deploymentSchedulerFactory;

    public DeploymentEventListener(DeploymentSchedulerFactory deploymentSchedulerFactory) {
        this.deploymentSchedulerFactory = deploymentSchedulerFactory;
    }

    @Async
    @TransactionalEventListener
    public void onIssuerDeploymentCreatedEvent(IssuerDeploymentCreatedEvent event) {
        IssuerDeployment issuerDeployment = event.getIssuerDeployment();
        if (issuerDeployment.getStatus() == Status.INIT) {
            deploy(issuerDeployment, event.getAuthKey());
        }
    }

    @Async
    @TransactionalEventListener
    public void onIssuerDeploymentDeletedEvent(IssuerDeploymentDeletedEvent event) {
        IssuerDeployment issuerDeployment = event.getIssuerDeployment();
        if (issuerDeployment.getStatus() == Status.DELETED_SCHEDULED) {
            delete(issuerDeployment);
        }
    }

    @Async
    @TransactionalEventListener
    public void onVerifierDeploymentCreatedEvent(VerifierDeploymentCreatedEvent event) {
        VerifierDeployment verifierDeployment = event.getVerifierDeployment();
        if (verifierDeployment.getStatus() == Status.INIT) {
            deploy(verifierDeployment, event.getAuthKey());
        }
    }

    @Async
    @TransactionalEventListener
    public void onVerifierDeploymentDeletedEvent(VerifierDeploymentDeletedEvent event) {
        VerifierDeployment verifierDeployment = event.getVerifierDeployment();
        if (verifierDeployment.getStatus() == Status.DELETED_SCHEDULED) {
            delete(verifierDeployment);
        }
    }

    @Async
    @TransactionalEventListener
    public void onIssuerDeploymentSecretUpgradeEvent(IssuerDeploymentSecretUpgradeEvent event) {
        IssuerDeployment issuerDeployment = event.getIssuerDeployment();
        if (issuerDeployment.getStatus() == Status.ACTIVE) {
            upgradeSecret(issuerDeployment, event.getAuthKey());
        }
    }

    @Async
    @TransactionalEventListener
    public void onIssuerDeploymentUpgradeEvent(IssuerDeploymentUpgradeEvent event) {
        IssuerDeployment issuerDeployment = event.getIssuerDeployment();
        if (issuerDeployment.getStatus() == Status.ACTIVE) {
            upgrade(issuerDeployment);
        }
    }

    @Async
    @TransactionalEventListener
    public void onVerifierDeploymentUpgradeEvent(VerifierDeploymentUpgradeEvent event) {
        VerifierDeployment verifierDeployment = event.getVerifierDeployment();
        if (verifierDeployment.getStatus() == Status.ACTIVE) {
            upgrade(verifierDeployment);
        }
    }

    @Async
    @TransactionalEventListener
    public void onVerifierDeploymentSecretUpgradeEvent(VerifierDeploymentSecretUpgradeEvent event) {
        VerifierDeployment verifierDeployment = event.getVerifierDeployment();
        if (verifierDeployment.getStatus() == Status.ACTIVE) {
            upgradeSecret(verifierDeployment, event.getAuthKey());
        }
    }

    private void deploy(VerifierDeployment deployment, String authKey) {
        DeploymentScheduler scheduler = deploymentSchedulerFactory.getScheduler(deployment.getDeploymentType());
        scheduler.scheduleDeployment(deployment, authKey);
    }

    private void delete(IssuerDeployment deployment) {
        DeploymentScheduler scheduler = deploymentSchedulerFactory.getScheduler(deployment.getDeploymentType());
        scheduler.deleteDeployment(deployment);
    }

    private void deploy(IssuerDeployment deployment, String authKey) {
        DeploymentScheduler scheduler = deploymentSchedulerFactory.getScheduler(deployment.getDeploymentType());
        scheduler.scheduleDeployment(deployment, authKey);
    }

    private void delete(VerifierDeployment deployment) {
        DeploymentScheduler scheduler = deploymentSchedulerFactory.getScheduler(deployment.getDeploymentType());
        scheduler.deleteDeployment(deployment);
    }

    private void upgradeSecret(IssuerDeployment deployment, String authKey) {
        DeploymentScheduler scheduler = deploymentSchedulerFactory.getScheduler(deployment.getDeploymentType());
        scheduler.scheduleClientSecretUpgrade(deployment, authKey);
    }


    private void upgrade(IssuerDeployment deployment) {
        DeploymentScheduler scheduler = deploymentSchedulerFactory.getScheduler(deployment.getDeploymentType());
        scheduler.scheduleUpgrade(deployment);
    }

    private void upgrade(VerifierDeployment deployment) {
        DeploymentScheduler scheduler = deploymentSchedulerFactory.getScheduler(deployment.getDeploymentType());
        scheduler.scheduleUpgrade(deployment);
    }

    private void upgradeSecret(VerifierDeployment deployment, String authKey) {
        DeploymentScheduler scheduler = deploymentSchedulerFactory.getScheduler(deployment.getDeploymentType());
        scheduler.scheduleClientSecretUpgrade(deployment, authKey);
    }
}
