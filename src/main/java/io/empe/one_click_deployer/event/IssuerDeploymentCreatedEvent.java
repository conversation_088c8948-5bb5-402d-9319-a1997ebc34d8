package io.empe.one_click_deployer.event;

import io.empe.one_click_deployer.entity.IssuerDeployment;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class IssuerDeploymentCreatedEvent extends ApplicationEvent {
    private final IssuerDeployment issuerDeployment;
    private final String authKey;

    public IssuerDeploymentCreatedEvent(Object source, IssuerDeployment issuerDeployment, String authKey) {
        super(source);
        this.issuerDeployment = issuerDeployment;
        this.authKey = authKey;
    }

}
