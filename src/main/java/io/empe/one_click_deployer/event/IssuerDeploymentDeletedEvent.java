package io.empe.one_click_deployer.event;

import io.empe.one_click_deployer.entity.IssuerDeployment;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class IssuerDeploymentDeletedEvent extends ApplicationEvent {
    private final IssuerDeployment issuerDeployment;

    public IssuerDeploymentDeletedEvent(Object source, IssuerDeployment issuerDeployment) {
        super(source);
        this.issuerDeployment = issuerDeployment;
    }

}
