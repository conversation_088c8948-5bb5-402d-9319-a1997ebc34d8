package io.empe.one_click_deployer.repository;

import io.empe.one_click_deployer.entity.BlockchainType;
import io.empe.one_click_deployer.entity.VerifierDeployment;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.UUID;

public interface VerifierDeploymentRepository extends JpaRepository<VerifierDeployment, UUID> {
    List<VerifierDeployment> findByUserId(UUID userId);

    boolean existsByVerifierName(String verifierName);

    boolean existsByBlockchainConfiguration_Type(BlockchainType type);

    Integer countByUserIdAndBlockchainConfiguration_Type(UUID userId, BlockchainType type);
}
