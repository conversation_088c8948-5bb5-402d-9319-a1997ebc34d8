package io.empe.one_click_deployer.repository;

import io.empe.one_click_deployer.entity.RefreshToken;
import org.springframework.data.jpa.repository.JpaRepository;

import java.time.LocalDateTime;
import java.util.Optional;

public interface RefreshTokenRepository extends JpaRepository<RefreshToken, Long> {
    Optional<RefreshToken> findByToken(String token);

    void deleteAllByExpiryDateBefore(LocalDateTime expiryDate);
}
