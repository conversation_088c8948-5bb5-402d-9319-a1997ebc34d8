package io.empe.one_click_deployer.repository;

import io.empe.one_click_deployer.entity.Token;
import io.empe.one_click_deployer.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface TokenRepository extends JpaRepository<Token, UUID> {
    Optional<Token> findByToken(String token);

    Optional<Token> findByUserAndType(User user, Token.TokenType type);

    Optional<Token> findByTokenAndType(String token, Token.TokenType type);

    void deleteByUserAndType(User user, Token.TokenType type);
}
