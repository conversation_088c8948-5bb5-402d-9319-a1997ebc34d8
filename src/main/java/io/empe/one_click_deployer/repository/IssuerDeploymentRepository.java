package io.empe.one_click_deployer.repository;

import io.empe.one_click_deployer.entity.BlockchainType;
import io.empe.one_click_deployer.entity.IssuerDeployment;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.UUID;

public interface IssuerDeploymentRepository extends JpaRepository<IssuerDeployment, UUID> {
    List<IssuerDeployment> findByUserId(UUID userId);

    boolean existsByIssuerName(String issuerName);

    boolean existsByBlockchainConfiguration_Type(BlockchainType type);

    Integer countByUserIdAndBlockchainConfiguration_Type(UUID userId, BlockchainType type);
}
