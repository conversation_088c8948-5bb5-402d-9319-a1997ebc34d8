package io.empe.one_click_deployer.repository;

import io.empe.one_click_deployer.entity.User;
import io.empe.one_click_deployer.entity.UserSubscription;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface UserSubscriptionRepository extends JpaRepository<UserSubscription, Long> {
    @EntityGraph(attributePaths = {"metadata"})
    Optional<UserSubscription> findByUserAndSubscriptionActiveTrue(User user);
}