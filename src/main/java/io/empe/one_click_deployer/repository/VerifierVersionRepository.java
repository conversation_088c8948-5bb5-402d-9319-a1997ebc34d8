package io.empe.one_click_deployer.repository;


import io.empe.one_click_deployer.entity.VerifierVersion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface VerifierVersionRepository extends JpaRepository<VerifierVersion, Long> {
    Optional<VerifierVersion> findByLabel(String label);
}
