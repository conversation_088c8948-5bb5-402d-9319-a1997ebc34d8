package io.empe.one_click_deployer.utils;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class VersionUtil {


    public static boolean isUpgradeValid(Version current, String newVersion) {
        Version next = parseVersion(newVersion);

        if (current == null || next == null) {
            return false; // Invalid version format
        }

        return next.isGreaterThan(current);
    }

    public static Version parseVersion(String version) {
        Pattern pattern = Pattern.compile("(\\d+)(?:\\.(\\d+))?(?:\\.(\\d+))?(?:-([a-zA-Z0-9]+))?");
        Matcher matcher = pattern.matcher(version);

        if (matcher.matches()) {
            int major = Integer.parseInt(matcher.group(1));
            int minor = (matcher.group(2) != null) ? Integer.parseInt(matcher.group(2)) : 0;
            int patch = (matcher.group(3) != null) ? Integer.parseInt(matcher.group(3)) : 0;
            String preRelease = (matcher.group(4) != null) ? matcher.group(4) : null;

            return new Version(major, minor, patch, preRelease);
        } else {
            return null; // Invalid version format
        }
    }

    static class Version {
        int major;
        int minor;
        int patch;
        String preRelease;

        public Version(int major, int minor, int patch, String preRelease) {
            this.major = major;
            this.minor = minor;
            this.patch = patch;
            this.preRelease = preRelease;
        }

        public boolean isGreaterThan(Version other) {
            if (this.major > other.major) {
                return true;
            } else if (this.major < other.major) {
                return false;
            }

            if (this.minor > other.minor) {
                return true;
            } else if (this.minor < other.minor) {
                return false;
            }

            if (this.patch > other.patch) {
                return true;
            } else if (this.patch < other.patch) {
                return false;
            }

            // Handle pre-release versions.  This is a simplified comparison.
            // More sophisticated pre-release comparison might be needed.
            if (this.preRelease != null && other.preRelease == null) {
                return true; // This version has a pre-release, other doesn't
            } else if (this.preRelease == null && other.preRelease != null) {
                return false; // Other version has a pre-release, this doesn't
            } else if (this.preRelease != null && other.preRelease != null) {
                // Compare pre-release strings lexicographically.  This is a basic approach.
                return this.preRelease.compareTo(other.preRelease) > 0;
            }

            return false; // Versions are equal
        }
    }

}
