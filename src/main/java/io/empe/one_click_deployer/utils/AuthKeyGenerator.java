package io.empe.one_click_deployer.utils;

import java.security.SecureRandom;

public class AuthKeyGenerator {

    private static final String LETTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    private static final String DIGITS = "0123456789";
    private static final String ALL_CHARACTERS = LETTERS + DIGITS;
    private static final int KEY_LENGTH = 32;
    private static final SecureRandom RANDOM = new SecureRandom();

    private AuthKeyGenerator() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    public static String generateAuthKey() {
        StringBuilder keyBuilder = new StringBuilder(KEY_LENGTH);

        // Ensure at least one letter, one digit, and one special character
        keyBuilder.append(LETTERS.charAt(RANDOM.nextInt(LETTERS.length())));
        keyBuilder.append(DIGITS.charAt(RANDOM.nextInt(DIGITS.length())));

        // Fill the rest of the key with random characters from the entire set
        for (int i = 3; i < KEY_LENGTH; i++) {
            keyBuilder.append(ALL_CHARACTERS.charAt(RANDOM.nextInt(ALL_CHARACTERS.length())));
        }

        // Shuffle the characters to avoid predictable patterns
        return shuffleString(keyBuilder.toString());
    }

    private static String shuffleString(String input) {
        char[] characters = input.toCharArray();
        for (int i = characters.length - 1; i > 0; i--) {
            int j = RANDOM.nextInt(i + 1);
            char temp = characters[i];
            characters[i] = characters[j];
            characters[j] = temp;
        }
        return new String(characters);
    }
}
