package io.empe.one_click_deployer.controller;

import io.empe.one_click_deployer.dto.*;
import io.empe.one_click_deployer.entity.User;
import io.empe.one_click_deployer.exception.external.*;
import io.empe.one_click_deployer.service.rest.IssuerDeploymentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

@RestController
@RequestMapping("/api/deployment/issuer")
@AllArgsConstructor
public class  IssuerDeploymentController {

    private final IssuerDeploymentService issuerDeploymentService;

    @Operation(
            summary = "Create a new issuer deployment",
            description = "Creates a new issuer deployment for the authenticated user, and deducts 1 credit.",
            tags = {"Deployment"},
            operationId = "createDeployment"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully created deployment",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = CreateIssuerDeploymentResponse.class))),
            @ApiResponse(responseCode = "409", description = "Issuer name already exists",
                    content = {@Content(mediaType = APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = ErrorResponseDto.class))})
    })
    @PostMapping("/create")
    public CreateIssuerDeploymentResponse createDeployment(@Valid @RequestBody CreateIssuerDeploymentRequest request) throws NotEnoughDeploymentLeft, IssuerNameAlreadyExistsExceptionBase, VersionNotExistsExceptionBase {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        return issuerDeploymentService.createDeployment(request, user);
    }

    @Operation(
            summary = "Get all deployments",
            description = "Fetches all deployments (for admins).",
            tags = {"Deployment"},
            operationId = "getAllDeployments"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved all deployments",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = IssuerDeploymentDto.class))),
    })
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public List<IssuerDeploymentDto> getAllDeployments() {
        return issuerDeploymentService.getAllDeployments();
    }

    @Operation(
            summary = "Get deployments of the logged-in user",
            description = "Fetches all deployments associated with the logged-in user.",
            tags = {"Deployment"},
            operationId = "getUserDeployments"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved user deployments",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = IssuerDeploymentDto.class))),
            @ApiResponse(responseCode = "404", description = "User has no deployments")
    })
    @GetMapping("/get/me")
    public List<IssuerDeploymentDto> getDeploymentsForLoggedInUser() {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        return issuerDeploymentService.getDeploymentsByUser(user);
    }

    @Operation(
            summary = "Get a specific deployment",
            description = "Fetches a specific deployment by its ID (Admin can fetch any, users can fetch only their own).",
            tags = {"Deployment"},
            operationId = "getDeploymentById"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the deployment",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = IssuerDeploymentDto.class))),
            @ApiResponse(responseCode = "404", description = "Deployment not found",
                    content = {@Content(mediaType = APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = ErrorResponseDto.class))}),
            @ApiResponse(responseCode = "403", description = "Forbidden - User trying to access another user's deployment",
                    content = {@Content(mediaType = APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = ErrorResponseDto.class))})
    })
    @GetMapping("/{id}")
    public IssuerDeploymentDto getDeployment(@PathVariable("id") UUID id) throws PermissionDeniedException, DeploymentNotFound {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        return issuerDeploymentService.getDeploymentById(id, user);
    }

    @Operation(
            summary = "Delete a specific deployment",
            description = "Deletes a deployment by its ID. Admins can delete any deployment, while users can delete only their own.",
            tags = {"Deployment"},
            operationId = "deleteDeployment"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Successfully deleted the deployment"),
            @ApiResponse(responseCode = "404", description = "Deployment not found",
                    content = {@Content(mediaType = APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = ErrorResponseDto.class))}),
            @ApiResponse(responseCode = "403", description = "Forbidden - User trying to delete another user's deployment",
                    content = {@Content(mediaType = APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = ErrorResponseDto.class))})
    })
    @DeleteMapping("/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteDeployment(@PathVariable UUID id) throws PermissionDeniedException, DeploymentNotFound {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        issuerDeploymentService.deleteDeploymentById(id, user);
    }

    @Operation(
            summary = "Update the client secret for a specific deployment",
            description = "Updates the client secret for the given verifier deployment and returns the new secret.",
            tags = {"Deployment"},
            operationId = "updateClientSecret"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully updated the client secret",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = UpdateClientSecretResponse.class))),
            @ApiResponse(responseCode = "404", description = "Deployment not found",
                    content = {@Content(mediaType = "application/json", schema = @Schema(implementation = ErrorResponseDto.class))}),
            @ApiResponse(responseCode = "403", description = "Forbidden - User trying to update another user's deployment",
                    content = {@Content(mediaType = "application/json", schema = @Schema(implementation = ErrorResponseDto.class))})
    })
    @PatchMapping("/{id}/update-client-secret")
    public UpdateClientSecretResponse updateClientSecret(@PathVariable UUID id) throws PermissionDeniedException, DeploymentNotFound {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        return issuerDeploymentService.updateClientSecret(id, user);
    }

    @Operation(
            summary = "Upgrade issuer deployment to a specific version",
            description = "Upgrades the issuer deployment to the specified version using issuer ID and version ID.",
            tags = {"Deployment"},
            operationId = "upgradeDeployment"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully upgraded the deployment",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = IssuerDeploymentDto.class))),
            @ApiResponse(responseCode = "404", description = "Deployment or version not found",
                    content = {@Content(mediaType = APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = ErrorResponseDto.class))}),
            @ApiResponse(responseCode = "403", description = "Forbidden - User trying to upgrade another user's deployment",
                    content = {@Content(mediaType = APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = ErrorResponseDto.class))})
    })
    @PatchMapping("/{issuerId}/upgrade/{versionId}")
    public IssuerDeploymentDto upgradeDeployment(@PathVariable UUID issuerId, @PathVariable Long versionId)
            throws PermissionDeniedException, DeploymentNotFound, VersionNotExistsExceptionBase, InvalidVersionUpgradeException {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        return issuerDeploymentService.upgradeDeployment(issuerId, versionId, user);
    }


}
