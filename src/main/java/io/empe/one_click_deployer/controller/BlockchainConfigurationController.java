package io.empe.one_click_deployer.controller;

import io.empe.one_click_deployer.dto.BlockchainConfigurationDto;
import io.empe.one_click_deployer.mapper.BlockchainConfigurationMapper;
import io.empe.one_click_deployer.repository.BlockchainConfigurationRepository;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/blockchain-configurations")
public class BlockchainConfigurationController {
    private final BlockchainConfigurationRepository repository;
    private final BlockchainConfigurationMapper mapper;

    public BlockchainConfigurationController(BlockchainConfigurationRepository repository, BlockchainConfigurationMapper mapper) {
        this.repository = repository;
        this.mapper = mapper;
    }

    // TODO: return only this configurations for the current user
    @GetMapping
    public List<BlockchainConfigurationDto> getAll() {
        return mapper.toDtoList(repository.findAll());
    }

    @GetMapping("/{id}")
    public ResponseEntity<BlockchainConfigurationDto> getById(@PathVariable Long id) {
        return repository.findById(id)
                .map(mapper::toDto)
                .map(ResponseEntity::ok)
                .orElseGet(() -> ResponseEntity.notFound().build());
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<BlockchainConfigurationDto> update(@PathVariable Long id, @RequestBody BlockchainConfigurationDto updatedDto) {
        return repository.findById(id)
                .map(existing -> {
                    mapper.updateEntityFromDto(updatedDto, existing);
                    repository.save(existing);
                    return ResponseEntity.ok(mapper.toDto(existing));
                })
                .orElseGet(() -> ResponseEntity.notFound().build());
    }
}
