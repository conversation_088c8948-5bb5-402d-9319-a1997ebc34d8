package io.empe.one_click_deployer.controller;

import io.empe.one_click_deployer.dto.*;
import io.empe.one_click_deployer.exception.external.*;
import io.empe.one_click_deployer.service.rest.AuthService;
import io.empe.one_click_deployer.service.rest.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequestMapping("/api")
@AllArgsConstructor
public class UserController {

    private final UserService userService;
    private final AuthenticationManager authenticationManager;
    private final AuthService authService;

    @Operation(
            summary = "Register a new user",
            description = "Creates a new user account with the provided details.",
            tags = {"Authentication"},
            operationId = "registerUser"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully logged in and token generated",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = LoginResponse.class))),
            @ApiResponse(responseCode = "400", description = "Bad Request - Invalid Input"),
            @ApiResponse(responseCode = "409", description = "Conflict - User already exists")
    })
    @PostMapping("/auth/signup")
    public ResponseEntity<RegisterUserResponse> registerUser(
            @Valid @RequestBody CreateUserRequest createUserRequest
    ) throws CreateUserInvalidParams, UserAlreadyExists, MailSendError {
        userService.registerUser(createUserRequest);
        UUID tokenUUID = authService.requestEmailConfirmation(createUserRequest.getEmail());
        return ResponseEntity.ok(new RegisterUserResponse(tokenUUID));
    }

    @Operation(
            summary = "Login user and generate a token",
            description = "Authenticates the user and returns a JWT token.",
            tags = {"Authentication"},
            operationId = "loginUser"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully logged in and token generated",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = LoginResponse.class))),
            @ApiResponse(responseCode = "400", description = "Bad Request - Invalid credentials or email not confirmed",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = ErrorResponseDto.class)))
    })
    @PostMapping("/auth/login")
    public ResponseEntity<LoginResponse> loginUser(@Valid @RequestBody LoginRequest loginRequest) throws UserEmailNotConfirmed {
        UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(
                loginRequest.getEmail(), loginRequest.getPassword());

        authenticationManager.authenticate(authenticationToken);
        LoginResponse loginResponse = userService.authenticateUser(loginRequest.getEmail(), loginRequest.getPassword());
        return ResponseEntity.ok(loginResponse);
    }

    @Operation(
            summary = "Refresh access token",
            description = "Generates a new access token using a valid refresh token.",
            tags = {"Authentication"},
            operationId = "refreshToken"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully refreshed token",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = LoginResponse.class))),
            @ApiResponse(responseCode = "400", description = "Bad Request - Invalid or expired refresh token",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = ErrorResponseDto.class)))
    })
    @PostMapping("/auth/refresh")
    public ResponseEntity<LoginResponse> refreshToken(@Valid @RequestBody RefreshTokenRequest refreshTokenRequest) throws RefreshTokenNotExistOrExpiredException {
        LoginResponse loginResponse = userService.refreshAccessToken(refreshTokenRequest.getRefreshToken());
        return ResponseEntity.ok(loginResponse);
    }


    @Operation(
            summary = "Get current user details",
            description = "Returns details of the currently authenticated user.",
            tags = {"User"},
            operationId = "getCurrentUser"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved user details",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = UserDto.class))),
            @ApiResponse(responseCode = "401", description = "Unauthorized - User is not authenticated",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = ErrorResponseDto.class)))
    })
    @GetMapping("/user/me")
    public ResponseEntity<UserDto> getUser() {
        return ResponseEntity.ok(userService.getCurrentUser());
    }
}
