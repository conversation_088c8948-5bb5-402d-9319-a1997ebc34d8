package io.empe.one_click_deployer.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.empe.one_click_deployer.repository.IssuerDeploymentRepository;
import io.empe.one_click_deployer.exception.external.GraylogEncodeException;
import io.empe.one_click_deployer.repository.VerifierDeploymentRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.time.ZonedDateTime;
import java.util.*;

@RestController
@RequestMapping("/api/logs")
@Slf4j
@RequiredArgsConstructor
public class GraylogController {

    private static final String GRAYLOG_STREAM_FILTER = "streams:683725efdc5cf02db9f3bda4";
    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final IssuerDeploymentRepository issuerDeploymentRepository;
    private final VerifierDeploymentRepository verifierDeploymentRepository;
    @Value("${graylog.token}")
    private String graylogToken;
    @Value("${graylog.url}")
    private String graylogUrl;

    @GetMapping("/issuer")
    public ResponseEntity<List<String>> getIssuerLogs(
            @RequestParam UUID issuerId,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @RequestParam(required = false) String phrase
    ) {
        return issuerDeploymentRepository.findById(issuerId)
                .map(issuer -> getLogsByDeploymentName(issuer.getIssuerName(), startTime, endTime, phrase))
                .orElseGet(() -> ResponseEntity.status(HttpStatus.NOT_FOUND).body(List.of("Issuer not found")));
    }

    @GetMapping("/verifier")
    public ResponseEntity<List<String>> getVerifierLogs(
            @RequestParam UUID verifierId,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @RequestParam(required = false) String phrase
    ) {
        return verifierDeploymentRepository.findById(verifierId)
                .map(verifier -> getLogsByDeploymentName(verifier.getVerifierName(), startTime, endTime, phrase))
                .orElseGet(() -> ResponseEntity.status(HttpStatus.NOT_FOUND).body(List.of("Verifier not found")));
    }

    private ResponseEntity<List<String>> getLogsByDeploymentName(String deploymentName, String startTime, String endTime, String phrase) {
        try {
            String url = buildGraylogUrl(deploymentName, startTime, endTime, phrase);

            HttpEntity<Void> request = new HttpEntity<>(buildHeaders());
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, request, String.class);
            
            List<String> logs = extractLogMessages(response.getBody());

            return ResponseEntity.ok(logs);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(List.of("Error: " + e.getMessage()));
        }
    }

    private HttpHeaders buildHeaders() {
        String credentials = Base64.getEncoder()
                .encodeToString(("token:" + graylogToken).getBytes(StandardCharsets.UTF_8));

        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Basic " + credentials);
        headers.set("Accept", "application/json");
        return headers;
    }


    private String buildGraylogUrl(String deploymentName, String startTime, String endTime, String phrase) throws GraylogEncodeException {

        String fromTime = startTime != null ? startTime : ZonedDateTime.now().minusMonths(1).toInstant().toString();
        String toTime = endTime != null ? endTime : new Date().toInstant().toString();

        String queryString = String.format("kubernetes_labels_app:%s", deploymentName);

        if (phrase != null && !phrase.trim().isEmpty()) {
            queryString += String.format(" AND %s", phrase);
        }

        return String.format("%s/api/search/universal/absolute?query=%s&from=%s&to=%s&filter=%s",
                graylogUrl, queryString, fromTime, toTime, GRAYLOG_STREAM_FILTER);
    }

    private List<String> extractLogMessages(String responseBody) throws Exception {
        JsonNode root = objectMapper.readTree(responseBody);
        JsonNode messages = root.path("messages");

        List<String> logMessages = new ArrayList<>();
        for (JsonNode entry : messages) {
            JsonNode messageNode = entry.path("message").path("message");
            if (!messageNode.isMissingNode()) {
                logMessages.add(messageNode.asText());
            }
        }
        return logMessages;
    }
}