package io.empe.one_click_deployer.controller;

import io.empe.one_click_deployer.dto.ErrorResponseDto;
import io.empe.one_click_deployer.exception.RestExceptionBase;
import io.empe.one_click_deployer.exception.UserErrorCode;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.AuthenticationException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class GlobalExceptionHandlerController {

    @ExceptionHandler(RestExceptionBase.class)
    public ResponseEntity<ErrorResponseDto> handleUserWrongParams(RestExceptionBase ex) {
        return createResponse(createErrorResponse(ex));
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ErrorResponseDto> handleValidationExceptions(MethodArgumentNotValidException ex) {
        StringBuilder errorMessage = new StringBuilder("Validation errors occurred: ");

        // Iterate through all validation errors
        ex.getBindingResult().getAllErrors().forEach((error) -> {
            String fieldName = ((FieldError) error).getField();
            String errorDescription = error.getDefaultMessage();

            // Append error description to the message
            errorMessage.append(String.format("Field '%s': %s. ", fieldName, errorDescription));
        });

        // Remove the last period and space from the end of the message
        if (!errorMessage.isEmpty()) {
            errorMessage.setLength(errorMessage.length() - 2);
        }

        // Create response with the error message
        ErrorResponseDto errorResponseDto = ErrorResponseDto
                .builder()
                .httpStatus(HttpStatus.BAD_REQUEST)
                .message(errorMessage.toString())
                .code(UserErrorCode.INVALID_USER_PARAMS.getCode())
                .build();

        return createResponse(errorResponseDto);
    }

    @ExceptionHandler(AuthenticationException.class)
    public ResponseEntity<ErrorResponseDto> handleGeneralException(AuthenticationException ex) {
        ErrorResponseDto errorResponseDto = ErrorResponseDto
                .builder()
                .httpStatus(HttpStatus.UNAUTHORIZED)
                .message(ex.getMessage())
                .code(UserErrorCode.UNAUTHORIZED.getCode())
                .build();

        return createResponse(errorResponseDto);
    }


    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponseDto> handleGeneralException(Exception ex) {
        ErrorResponseDto errorResponseDto = ErrorResponseDto
                .builder()
                .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                .message(ex.getMessage())
                .code(UserErrorCode.GENERAL_ERROR.getCode())
                .build();

        return createResponse(errorResponseDto);
    }

    public ResponseEntity<ErrorResponseDto> createResponse(ErrorResponseDto errorResponse) {
        return ResponseEntity
                .status(errorResponse.getHttpStatus())
                .body(errorResponse);
    }

    public ErrorResponseDto createErrorResponse(RestExceptionBase exception) {
        return ErrorResponseDto.builder()
                .message(exception.getMessage())
                .code(exception.getCode())
                .httpStatus(exception.getHttpStatus())
                .build();
    }
}
