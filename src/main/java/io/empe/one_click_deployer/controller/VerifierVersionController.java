package io.empe.one_click_deployer.controller;

import io.empe.one_click_deployer.dto.CreateVerifierVersionRequest;
import io.empe.one_click_deployer.dto.ErrorResponseDto;
import io.empe.one_click_deployer.dto.VerifierVersionDto;
import io.empe.one_click_deployer.exception.external.VerifierVersionNotFoundException;
import io.empe.one_click_deployer.service.rest.VerifierVersionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

@RestController
@RequestMapping("/api/verifier-version")
@AllArgsConstructor
public class VerifierVersionController {

    private final VerifierVersionService verifierVersionService;

    @Operation(
            summary = "Get all verifier versions",
            description = "Fetches all verifier versions. Admin access required.",
            tags = {"Verifier Version"},
            operationId = "getAllVerifierVersions"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved all verifier versions",
                    content = @Content(mediaType = APPLICATION_JSON_VALUE, schema = @Schema(implementation = VerifierVersionDto.class))),
    })
    @GetMapping
    public List<VerifierVersionDto> getAll() {
        return verifierVersionService.findAll();
    }

    @Operation(
            summary = "Get a specific verifier version",
            description = "Fetches a specific verifier version by its ID.",
            tags = {"Verifier Version"},
            operationId = "getVerifierVersionById"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the verifier version",
                    content = @Content(mediaType = APPLICATION_JSON_VALUE, schema = @Schema(implementation = VerifierVersionDto.class))),
            @ApiResponse(responseCode = "404", description = "Verifier version not found",
                    content = @Content(mediaType = APPLICATION_JSON_VALUE, schema = @Schema(implementation = ErrorResponseDto.class)))
    })
    @GetMapping("/{id}")
    public VerifierVersionDto getById(@PathVariable Long id) throws VerifierVersionNotFoundException {
        return verifierVersionService.findById(id);
    }

    @Operation(
            summary = "Create a new verifier version",
            description = "Creates a new verifier version with the provided data.",
            tags = {"Verifier Version"},
            operationId = "createVerifierVersion"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Successfully created the verifier version",
                    content = @Content(mediaType = APPLICATION_JSON_VALUE, schema = @Schema(implementation = VerifierVersionDto.class))),
            @ApiResponse(responseCode = "400", description = "Invalid request data",
                    content = @Content(mediaType = APPLICATION_JSON_VALUE, schema = @Schema(implementation = ErrorResponseDto.class)))
    })
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @PreAuthorize("hasRole('ADMIN')")
    public VerifierVersionDto create(@Valid @RequestBody CreateVerifierVersionRequest request) {
        return verifierVersionService.create(request);
    }

    @Operation(
            summary = "Delete a specific verifier version",
            description = "Deletes a verifier version by its ID. Admin access required.",
            tags = {"Verifier Version"},
            operationId = "deleteVerifierVersion"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Successfully deleted the verifier version"),
            @ApiResponse(responseCode = "404", description = "Verifier version not found",
                    content = @Content(mediaType = APPLICATION_JSON_VALUE, schema = @Schema(implementation = ErrorResponseDto.class)))
    })

    @DeleteMapping("/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PreAuthorize("hasRole('ADMIN')")
    public void delete(@PathVariable Long id) throws VerifierVersionNotFoundException {
        verifierVersionService.delete(id);
    }
}
