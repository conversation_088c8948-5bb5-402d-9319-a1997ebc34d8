package io.empe.one_click_deployer.controller;

import io.empe.one_click_deployer.dto.CreateIssuerVersionRequest;
import io.empe.one_click_deployer.dto.ErrorResponseDto;
import io.empe.one_click_deployer.dto.IssuerVersionDto;
import io.empe.one_click_deployer.exception.external.IssuerVersionNotFoundException;
import io.empe.one_click_deployer.service.rest.IssuerVersionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

@RestController
@RequestMapping("/api/issuer-version")
@AllArgsConstructor
public class IssuerVersionController {

    private final IssuerVersionService issuerVersionService;

    @Operation(
            summary = "Get all issuer versions",
            description = "Fetches all issuer versions. Admin access required.",
            tags = {"Issuer Version"},
            operationId = "getAllIssuerVersions"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved all issuer versions",
                    content = @Content(mediaType = APPLICATION_JSON_VALUE, schema = @Schema(implementation = IssuerVersionDto.class))),
    })
    @GetMapping
    public List<IssuerVersionDto> getAll() {
        return issuerVersionService.findAll();
    }

    @Operation(
            summary = "Get a specific issuer version",
            description = "Fetches a specific issuer version by its ID.",
            tags = {"Issuer Version"},
            operationId = "getIssuerVersionById"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the issuer version",
                    content = @Content(mediaType = APPLICATION_JSON_VALUE, schema = @Schema(implementation = IssuerVersionDto.class))),
            @ApiResponse(responseCode = "404", description = "Issuer version not found",
                    content = @Content(mediaType = APPLICATION_JSON_VALUE, schema = @Schema(implementation = ErrorResponseDto.class)))
    })
    @GetMapping("/{id}")
    public IssuerVersionDto getById(@PathVariable Long id) throws IssuerVersionNotFoundException {
        return issuerVersionService.findById(id);
    }

    @Operation(
            summary = "Create a new issuer version",
            description = "Creates a new issuer version with the provided data.",
            tags = {"Issuer Version"},
            operationId = "createIssuerVersion"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Successfully created the issuer version",
                    content = @Content(mediaType = APPLICATION_JSON_VALUE, schema = @Schema(implementation = IssuerVersionDto.class))),
            @ApiResponse(responseCode = "400", description = "Invalid request data",
                    content = @Content(mediaType = APPLICATION_JSON_VALUE, schema = @Schema(implementation = ErrorResponseDto.class)))
    })
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @PreAuthorize("hasRole('ADMIN')")
    public IssuerVersionDto create(@Valid @RequestBody CreateIssuerVersionRequest request) {
        return issuerVersionService.create(request);
    }

    @Operation(
            summary = "Delete a specific issuer version",
            description = "Deletes an issuer version by its ID. Admin access required.",
            tags = {"Issuer Version"},
            operationId = "deleteIssuerVersion"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Successfully deleted the issuer version"),
            @ApiResponse(responseCode = "404", description = "Issuer version not found",
                    content = @Content(mediaType = APPLICATION_JSON_VALUE, schema = @Schema(implementation = ErrorResponseDto.class)))
    })

    @DeleteMapping("/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PreAuthorize("hasRole('ADMIN')")
    public void delete(@PathVariable Long id) throws IssuerVersionNotFoundException {
        issuerVersionService.delete(id);
    }
}
