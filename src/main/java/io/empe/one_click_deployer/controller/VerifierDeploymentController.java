package io.empe.one_click_deployer.controller;

import io.empe.one_click_deployer.dto.*;
import io.empe.one_click_deployer.entity.User;
import io.empe.one_click_deployer.exception.external.*;
import io.empe.one_click_deployer.service.rest.VerifierDeploymentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

@RestController
@RequestMapping("/api/deployment/verifier")
@AllArgsConstructor
public class VerifierDeploymentController {

    private final VerifierDeploymentService verifierDeploymentService;

    @Operation(
            summary = "Create a new verifier deployment",
            description = "Creates a new verifier deployment for the authenticated user, and deducts 1 credit.",
            tags = {"Deployment"},
            operationId = "createDeployment"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully created deployment",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = CreateVerifierDeploymentResponse.class))),
            @ApiResponse(responseCode = "409", description = "Verifier name already exists",
                    content = {@Content(mediaType = APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = ErrorResponseDto.class))})
    })
    @PostMapping("/create")
    public CreateVerifierDeploymentResponse createDeployment(@Valid @RequestBody CreateVerifierDeploymentRequest request) throws NotEnoughDeploymentLeft, VerifierNameAlreadyExistsExceptionBase, VersionNotExistsExceptionBase {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        return verifierDeploymentService.createDeployment(request, user);
    }

    @Operation(
            summary = "Get all deployments",
            description = "Fetches all deployments (for admins).",
            tags = {"Deployment"},
            operationId = "getAllDeployments"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved all deployments",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = VerifierDeploymentDto.class))),
    })
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public List<VerifierDeploymentDto> getAllDeployments() {
        return verifierDeploymentService.getAllDeployments();
    }

    @Operation(
            summary = "Get deployments of the logged-in user",
            description = "Fetches all deployments associated with the logged-in user.",
            tags = {"Deployment"},
            operationId = "getUserDeployments"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved user deployments",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = VerifierDeploymentDto.class))),
            @ApiResponse(responseCode = "404", description = "User has no deployments")
    })
    @GetMapping("/get/me")
    public List<VerifierDeploymentDto> getDeploymentsForLoggedInUser() {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        return verifierDeploymentService.getDeploymentsByUser(user);
    }

    @Operation(
            summary = "Get a specific deployment",
            description = "Fetches a specific deployment by its ID (Admin can fetch any, users can fetch only their own).",
            tags = {"Deployment"},
            operationId = "getDeploymentById"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved the deployment",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = VerifierDeploymentDto.class))),
            @ApiResponse(responseCode = "404", description = "Deployment not found",
                    content = {@Content(mediaType = APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = ErrorResponseDto.class))}),
            @ApiResponse(responseCode = "403", description = "Forbidden - User trying to access another user's deployment",
                    content = {@Content(mediaType = APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = ErrorResponseDto.class))})
    })
    @GetMapping("/{id}")
    public VerifierDeploymentDto getDeployment(@PathVariable UUID id) throws PermissionDeniedException, DeploymentNotFound {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        return verifierDeploymentService.getDeploymentById(id, user);
    }

    @Operation(
            summary = "Delete a specific deployment",
            description = "Deletes a deployment by its ID. Admins can delete any deployment, while users can delete only their own.",
            tags = {"Deployment"},
            operationId = "deleteDeployment"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Successfully deleted the deployment"),
            @ApiResponse(responseCode = "404", description = "Deployment not found",
                    content = {@Content(mediaType = APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = ErrorResponseDto.class))}),
            @ApiResponse(responseCode = "403", description = "Forbidden - User trying to delete another user's deployment",
                    content = {@Content(mediaType = APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = ErrorResponseDto.class))})
    })
    @DeleteMapping("/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteDeployment(@PathVariable UUID id) throws PermissionDeniedException, DeploymentNotFound {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        verifierDeploymentService.deleteDeploymentById(id, user);
    }

    @Operation(
            summary = "Update the client secret for a specific deployment",
            description = "Updates the client secret for the given verifier deployment and returns the new secret.",
            tags = {"Deployment"},
            operationId = "updateClientSecret"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully updated the client secret",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = UpdateClientSecretResponse.class))),
            @ApiResponse(responseCode = "404", description = "Deployment not found",
                    content = {@Content(mediaType = "application/json", schema = @Schema(implementation = ErrorResponseDto.class))}),
            @ApiResponse(responseCode = "403", description = "Forbidden - User trying to update another user's deployment",
                    content = {@Content(mediaType = "application/json", schema = @Schema(implementation = ErrorResponseDto.class))})
    })
    @PatchMapping("/{id}/update-client-secret")
    public UpdateClientSecretResponse updateClientSecret(@PathVariable UUID id) throws PermissionDeniedException, DeploymentNotFound {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        return verifierDeploymentService.updateClientSecret(id, user);
    }

    @Operation(
            summary = "Upgrade verifier deployment to a specific version",
            description = "Upgrades the verifier deployment to the specified version using verifier ID and version ID.",
            tags = {"Deployment"},
            operationId = "upgradeDeployment"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully upgraded the deployment",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = VerifierDeploymentDto.class))),
            @ApiResponse(responseCode = "404", description = "Deployment or version not found",
                    content = {@Content(mediaType = APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = ErrorResponseDto.class))}),
            @ApiResponse(responseCode = "403", description = "Forbidden - User trying to upgrade another user's deployment",
                    content = {@Content(mediaType = APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = ErrorResponseDto.class))})
    })
    @PatchMapping("/{verifierId}/upgrade/{versionId}")
    public VerifierDeploymentDto upgradeDeployment(@PathVariable UUID verifierId, @PathVariable Long versionId)
            throws PermissionDeniedException, DeploymentNotFound, VersionNotExistsExceptionBase, InvalidVersionUpgradeException {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        return verifierDeploymentService.upgradeDeployment(verifierId, versionId, user);
    }

}
