package io.empe.one_click_deployer.controller;

import io.empe.one_click_deployer.dto.*;
import io.empe.one_click_deployer.exception.external.InvalidToken;
import io.empe.one_click_deployer.exception.external.MailSendError;
import io.empe.one_click_deployer.service.rest.AuthService;
import io.empe.one_click_deployer.service.rest.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
public class AuthController {
    private final AuthService authService;
    private final UserService userService;

    @Operation(
            summary = "Confirm user e-mail",
            description = "Verifies the confirmation token, activates the user account and returns access tokens.",
            tags = {"Authentication"},
            operationId = "confirmEmail"
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "E-mail confirmed – tokens issued",
                    content = @Content(schema = @Schema(implementation = LoginResponse.class))),
            @ApiResponse(responseCode = "400", description = "Invalid or expired token",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = ErrorResponseDto.class)))
    })
    @PostMapping("/confirm-email")
    public ResponseEntity<LoginResponse> confirmEmail(@Valid @RequestBody EmailConfirmationRequest body) throws InvalidToken {
        var confirmationResponse = authService.confirmEmail(body.getUuid(), body.getToken());
        return ResponseEntity.ok(confirmationResponse);
    }

    @Operation(
            summary = "Resend confirmation e-mail",
            description = "Sends a new confirmation e-mail if the address exists and is still unconfirmed. " +
                    "Always returns 200 to avoid leaking whether the e-mail is registered.",
            tags = {"Authentication"},
            operationId = "resendEmailConfirmation"
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200",
                    description = "Confirmation e-mail resent or no action needed"),
            @ApiResponse(responseCode = "400",
                    description = "Bad Request – Invalid e-mail",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = ErrorResponseDto.class)))
    })
    @PostMapping("/resend-confirmation")
    public ResponseEntity<Void> resendEmailConfirmation(
            @Valid @RequestBody ResendEmailConfirmationRequest body) throws MailSendError {

        authService.resendEmailConfirmation(body.getUuid());
        return ResponseEntity.ok().build();
    }

    @Operation(
            summary = "Send password-reset e-mail",
            description = "Triggers an e-mail with a reset link for the given address.",
            tags = {"Authentication"},
            operationId = "requestPasswordReset"
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200",
                    description = "Reset link sent (even if the user does not exist)"),
            @ApiResponse(responseCode = "400",
                    description = "Bad Request - Invalid e-mail",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = ErrorResponseDto.class)))
    })
    @PostMapping("/request-reset")
    public ResponseEntity<Void> requestPasswordReset(
            @Valid @RequestBody PasswordResetRequest body) throws MailSendError {
        authService.requestPasswordReset(body.getEmail());
        return ResponseEntity.ok().build();
    }

    @Operation(
            summary = "Get current user details",
            description = "Returns details of the currently authenticated user.",
            tags = {"User"},
            operationId = "getCurrentUser"
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200",
                    description = "Reset link sent (even if the user does not exist)"),
            @ApiResponse(responseCode = "400",
                    description = "Bad Request - Invalid e-mail",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = ErrorResponseDto.class)))
    })
    @PostMapping("/change-password")
    public ResponseEntity<Void> changePassword() throws MailSendError {
        authService.requestPasswordReset(userService.getCurrentUser().getEmail());
        return ResponseEntity.ok().build();
    }

    @Operation(
            summary = "Reset password with token",
            description = "Resets the user’s password using the token received by e-mail.",
            tags = {"Authentication"},
            operationId = "resetPassword"
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200",
                    description = "Password reset successfully"),
            @ApiResponse(responseCode = "400",
                    description = "Invalid or expired token",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = ErrorResponseDto.class)))
    })
    @PostMapping("/reset-password")
    public ResponseEntity<Void> resetPassword(
            @Valid
            @RequestBody()
            ResetPasswordRequest body) throws InvalidToken {

        authService.resetPassword(body.getUuid(), body.getToken(), body.getNewPassword());
        return ResponseEntity.ok().build();
    }
}