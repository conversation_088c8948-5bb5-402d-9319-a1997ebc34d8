package io.empe.one_click_deployer.controller;

import com.stripe.exception.StripeException;
import io.empe.one_click_deployer.service.rest.StripeService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/stripe")
@AllArgsConstructor
public class StripeController {
    private final StripeService stripeService;

    @PostMapping("/webhook")
    public ResponseEntity<String> handleWebhook(
            @RequestBody String payload,
            @RequestHeader("Stripe-Signature") String sigHeader) {
        try {
            stripeService.handleWebhook(payload, sigHeader);
            return ResponseEntity.ok("Webhook received");
        } catch (Exception e) {
            log.info("Webhook error: {}", e.getMessage());
            return ResponseEntity.badRequest().body("Webhook error: " + e.getMessage());
        }
    }

    @GetMapping("/portal")
    public ResponseEntity<Map<String, String>> customerPortal() {
        try {
            String portalUrl = stripeService.createCustomerPortalSession();

            return ResponseEntity.ok(Map.of("url", portalUrl));
        } catch (StripeException e) {
            log.error("Błąd tworzenia sesji Customer Portal: {}", e.getMessage(), e);
            return ResponseEntity
                    .status(500)
                    .body(Map.of("error", "Nie udało się wygenerować linku do portalu"));
        }
    }
}
