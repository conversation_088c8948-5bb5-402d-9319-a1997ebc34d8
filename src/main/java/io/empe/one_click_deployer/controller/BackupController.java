package io.empe.one_click_deployer.controller;

import io.empe.one_click_deployer.entity.User;
import io.empe.one_click_deployer.service.database.DatabaseBackupService;
import io.empe.one_click_deployer.service.database.DatabaseBackupService.BackupFileInfo;
import io.empe.one_click_deployer.exception.external.DeploymentNotFound;
import io.empe.one_click_deployer.exception.external.PermissionDeniedException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.AllArgsConstructor;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/api/backups")
@AllArgsConstructor
public class BackupController {

    private final DatabaseBackupService databaseBackupService;

    @Operation(
            summary = "List database backups",
            description = "Retrieves a list of database backups stored in S3. Admins see all backups, regular users see only their own.",
            tags = {"Backup"},
            operationId = "listBackups"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved backup list",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = BackupFileInfo.class)))
    })
    @GetMapping
    public List<BackupFileInfo> listBackups() {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        // Admins can see all backups, regular users see only their own
        if (user.getRole() == User.Role.ADMIN) {
            return databaseBackupService.listBackups();
        } else {
            return databaseBackupService.listBackupsForUser(user);
        }
    }

    @Operation(
            summary = "Download a specific backup file",
            description = "Downloads a specific backup file from S3. Users can only download backups for their own deployments.",
            tags = {"Backup"},
            operationId = "downloadBackup"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully downloaded backup file",
                    content = @Content(mediaType = "application/octet-stream")),
            @ApiResponse(responseCode = "404", description = "Backup file not found"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Access denied to this backup")
    })
    @GetMapping("/download")
    public ResponseEntity<InputStreamResource> downloadBackup(@RequestParam String fileName) {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        // Check if user can access this backup
        if (!databaseBackupService.canUserAccessBackup(user, fileName)) {
            return ResponseEntity.status(403).build();
        }

        try {
            ResponseInputStream<GetObjectResponse> s3Object = databaseBackupService.downloadBackup(fileName);

            // Extract the actual filename from the path for the download
            String downloadFileName = fileName.contains("/") ? 
                fileName.substring(fileName.lastIndexOf("/") + 1) : fileName;

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + downloadFileName + "\"")
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(new InputStreamResource(s3Object));

        } catch (Exception e) {
            return ResponseEntity.notFound().build();
        }
    }

    @Operation(
            summary = "Trigger manual backup",
            description = "Manually triggers backup of all issuer and verifier databases. Only accessible by admins.",
            tags = {"Backup"},
            operationId = "triggerBackup"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Backup process started successfully"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Admin access required")
    })
    @PostMapping("/trigger")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<String> triggerManualBackup() {
        try {
            databaseBackupService.backupIssuerDatabases();
            databaseBackupService.backupVerifierDatabases();
            return ResponseEntity.ok("Backup process completed successfully");
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body("Failed to complete backup process: " + e.getMessage());
        }
    }

    @Operation(
            summary = "Create backup for specific issuer",
            description = "Creates a manual backup for a specific issuer deployment. Users can only backup their own deployments.",
            tags = {"Backup"},
            operationId = "createIssuerBackup"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Backup process started successfully"),
            @ApiResponse(responseCode = "404", description = "Issuer deployment not found"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Access denied to this deployment")
    })
    @PostMapping("/issuer/{id}")
    public ResponseEntity<String> createIssuerBackup(@PathVariable UUID id) {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        try {
            databaseBackupService.backupIssuerById(id, user);
            return ResponseEntity.ok("Issuer backup process started successfully");
        } catch (DeploymentNotFound e) {
            return ResponseEntity.notFound().build();
        } catch (PermissionDeniedException e) {
            return ResponseEntity.status(403).body("Access denied to this deployment");
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body("Failed to start backup process: " + e.getMessage());
        }
    }

    @Operation(
            summary = "Create backup for specific verifier",
            description = "Creates a manual backup for a specific verifier deployment. Users can only backup their own deployments.",
            tags = {"Backup"},
            operationId = "createVerifierBackup"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Backup process started successfully"),
            @ApiResponse(responseCode = "404", description = "Verifier deployment not found"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Access denied to this deployment")
    })
    @PostMapping("/verifier/{id}")
    public ResponseEntity<String> createVerifierBackup(@PathVariable UUID id) {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        try {
            databaseBackupService.backupVerifierById(id, user);
            return ResponseEntity.ok("Verifier backup process started successfully");
        } catch (DeploymentNotFound e) {
            return ResponseEntity.notFound().build();
        } catch (PermissionDeniedException e) {
            return ResponseEntity.status(403).body("Access denied to this deployment");
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body("Failed to start backup process: " + e.getMessage());
        }
    }

    @Operation(
            summary = "List backups for specific issuer",
            description = "Retrieves a list of database backups for a specific issuer deployment. Users can only view backups for their own deployments.",
            tags = {"Backup"},
            operationId = "listIssuerBackups"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved issuer backup list",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = BackupFileInfo.class))),
            @ApiResponse(responseCode = "404", description = "Issuer deployment not found"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Access denied to this deployment")
    })
    @GetMapping("/issuer/{id}")
    public ResponseEntity<List<BackupFileInfo>> listIssuerBackups(@PathVariable UUID id) {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        try {
            List<BackupFileInfo> backups = databaseBackupService.listBackupsForIssuer(id, user);
            return ResponseEntity.ok(backups);
        } catch (DeploymentNotFound e) {
            return ResponseEntity.notFound().build();
        } catch (PermissionDeniedException e) {
            return ResponseEntity.status(403).build();
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @Operation(
            summary = "List backups for specific verifier",
            description = "Retrieves a list of database backups for a specific verifier deployment. Users can only view backups for their own deployments.",
            tags = {"Backup"},
            operationId = "listVerifierBackups"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved verifier backup list",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = BackupFileInfo.class))),
            @ApiResponse(responseCode = "404", description = "Verifier deployment not found"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Access denied to this deployment")
    })
    @GetMapping("/verifier/{id}")
    public ResponseEntity<List<BackupFileInfo>> listVerifierBackups(@PathVariable UUID id) {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        try {
            List<BackupFileInfo> backups = databaseBackupService.listBackupsForVerifier(id, user);
            return ResponseEntity.ok(backups);
        } catch (DeploymentNotFound e) {
            return ResponseEntity.notFound().build();
        } catch (PermissionDeniedException e) {
            return ResponseEntity.status(403).build();
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @Operation(
            summary = "Restore issuer database from backup",
            description = "Restores an issuer database from a specific backup file. This will completely replace the current database with the backup data. Users can only restore their own deployments.",
            tags = {"Backup"},
            operationId = "restoreIssuerDatabase"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Database restore process started successfully"),
            @ApiResponse(responseCode = "404", description = "Issuer deployment not found"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Access denied to this deployment or backup file"),
            @ApiResponse(responseCode = "400", description = "Invalid backup file name")
    })
    @PostMapping("/issuer/{id}/restore")
    public ResponseEntity<String> restoreIssuerDatabase(@PathVariable UUID id, @RequestParam String backupFileName) {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        if (backupFileName == null || backupFileName.trim().isEmpty()) {
            return ResponseEntity.badRequest().body("Backup file name is required");
        }

        try {
            databaseBackupService.restoreIssuerDatabase(id, backupFileName, user);
            return ResponseEntity.ok("Issuer database restore completed successfully.");
        } catch (DeploymentNotFound e) {
            return ResponseEntity.notFound().build();
        } catch (PermissionDeniedException e) {
            return ResponseEntity.status(403).body("Access denied to this deployment or backup file");
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body("Failed to complete restore process: " + e.getMessage());
        }
    }

    @Operation(
            summary = "Restore verifier database from backup",
            description = "Restores a verifier database from a specific backup file. This will completely replace the current database with the backup data. Users can only restore their own deployments.",
            tags = {"Backup"},
            operationId = "restoreVerifierDatabase"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Database restore process started successfully"),
            @ApiResponse(responseCode = "404", description = "Verifier deployment not found"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Access denied to this deployment or backup file"),
            @ApiResponse(responseCode = "400", description = "Invalid backup file name")
    })
    @PostMapping("/verifier/{id}/restore")
    public ResponseEntity<String> restoreVerifierDatabase(@PathVariable UUID id, @RequestParam String backupFileName) {
        User user = (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        if (backupFileName == null || backupFileName.trim().isEmpty()) {
            return ResponseEntity.badRequest().body("Backup file name is required");
        }

        try {
            databaseBackupService.restoreVerifierDatabase(id, backupFileName, user);
            return ResponseEntity.ok("Verifier database restore completed successfully.");
        } catch (DeploymentNotFound e) {
            return ResponseEntity.notFound().build();
        } catch (PermissionDeniedException e) {
            return ResponseEntity.status(403).body("Access denied to this deployment or backup file");
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body("Failed to complete restore process: " + e.getMessage());
        }
    }
}
