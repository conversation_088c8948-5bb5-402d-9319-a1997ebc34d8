package io.empe.one_click_deployer.mapper;

import io.empe.one_click_deployer.dto.MetadataDto;
import io.empe.one_click_deployer.entity.Metadata;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Set;

@Mapper
public interface SubscriptionMapper {
    SubscriptionMapper INSTANCE = Mappers.getMapper(SubscriptionMapper.class);


    List<MetadataDto> toMetadataDtoList(Set<Metadata> metadata);
}
