package io.empe.one_click_deployer.mapper;

import io.empe.one_click_deployer.dto.IssuerDeploymentDto;
import io.empe.one_click_deployer.entity.IssuerDeployment;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface IssuerDeploymentMapper {
    IssuerDeploymentMapper INSTANCE = Mappers.getMapper(IssuerDeploymentMapper.class);

    @Mapping(source = "user.id", target = "userId")
    @Mapping(source = "blockchainConfiguration.type", target = "networkName")
    @Mapping(source = "issuerVersion.version", target = "version")
    IssuerDeploymentDto toDto(IssuerDeployment issuerDeployment);

    @Mapping(target = "user", ignore = true)
    @Mapping(target = "issuerVersion", ignore = true)
    IssuerDeployment toEntity(IssuerDeploymentDto issuerDeploymentDto);
}
