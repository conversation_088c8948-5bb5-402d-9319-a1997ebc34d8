package io.empe.one_click_deployer.mapper;

import io.empe.one_click_deployer.dto.BlockchainConfigurationDto;
import io.empe.one_click_deployer.entity.BlockchainConfiguration;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

import java.util.List;

@Mapper(componentModel = "spring")
public interface BlockchainConfigurationMapper {
    BlockchainConfigurationDto toDto(BlockchainConfiguration entity);

    BlockchainConfiguration toEntity(BlockchainConfigurationDto dto);

    List<BlockchainConfigurationDto> toDtoList(List<BlockchainConfiguration> entities);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void updateEntityFromDto(BlockchainConfigurationDto dto, @MappingTarget BlockchainConfiguration entity);
}
