package io.empe.one_click_deployer.mapper;

import io.empe.one_click_deployer.dto.VerifierDeploymentDto;
import io.empe.one_click_deployer.entity.VerifierDeployment;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface VerifierDeploymentMapper {
    VerifierDeploymentMapper INSTANCE = Mappers.getMapper(VerifierDeploymentMapper.class);

    @Mapping(source = "user.id", target = "userId")
    @Mapping(source = "verifierVersion.version", target = "version")
    @Mapping(source = "blockchainConfiguration.type", target = "networkName")
    VerifierDeploymentDto toDto(VerifierDeployment verifierDeployment);

    @Mapping(target = "user", ignore = true)
    @Mapping(target = "verifierVersion", ignore = true)
    VerifierDeployment toEntity(VerifierDeploymentDto verifierDeploymentDto);
}
