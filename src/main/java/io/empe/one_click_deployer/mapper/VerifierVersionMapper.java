package io.empe.one_click_deployer.mapper;

import io.empe.one_click_deployer.dto.CreateVerifierVersionRequest;
import io.empe.one_click_deployer.dto.VerifierVersionDto;
import io.empe.one_click_deployer.entity.VerifierVersion;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface VerifierVersionMapper {

    VerifierVersionDto toDto(VerifierVersion verifierVersion);

    VerifierVersion toEntity(VerifierVersionDto verifierVersionDto);

    @Mapping(target = "id", ignore = true)
    VerifierVersion toEntity(CreateVerifierVersionRequest request);
} 