package io.empe.one_click_deployer.mapper;

import io.empe.one_click_deployer.dto.CreateIssuerVersionRequest;
import io.empe.one_click_deployer.dto.IssuerVersionDto;
import io.empe.one_click_deployer.entity.IssuerVersion;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface IssuerVersionMapper {

    IssuerVersionDto toDto(IssuerVersion issuerVersion);

    IssuerVersion toEntity(IssuerVersionDto issuerVersionDto);

    @Mapping(target = "id", ignore = true)
    IssuerVersion toEntity(CreateIssuerVersionRequest request);
} 