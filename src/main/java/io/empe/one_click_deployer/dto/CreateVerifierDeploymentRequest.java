package io.empe.one_click_deployer.dto;

import io.empe.one_click_deployer.entity.DeploymentType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateVerifierDeploymentRequest {

    @NotBlank(message = "Verifier name is required")
    @Pattern(regexp = "^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$",
            message = "Verifier name must match the regex ^[a-z0-9]([-a-z0-9]*[a-z0-9])?(\\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*$")
    @Size(max = 53, message = "Verifier name must not be longer than 53 characters")
    @Schema(
            description = "The name of the verifier to be deployed. This field is required and serves as a unique identifier for the verifier's deployment.",
            example = "VeriTech"
    )
    private String verifierName;

    @NotBlank(message = "Verifier readable name is required")
    @Size(max = 100, message = "Verifier readable name must not be longer than 100 characters")
    @Schema(
            description = "A human-readable name for the verifier that can be displayed in UIs and logs.",
            example = "BigStar Verifier Service"
    )
    private String verifierReadableName;

    @NotNull(message = "Deployment type is required")
    @Schema(
            description = "The type of deployment to be performed. This field is required and defines the deployment strategy.",
            example = "EMPE_OVH_K8S_DEPLOYMENT"
    )
    private DeploymentType deploymentType;

    @Schema(
            description = "The ID of the version to be used in the deployment. This field is optional.",
            example = "42"
    )
    private Long versionId;

    @Schema(
            description = "The ID of the blockchain configuration to use for this deployment (required, only one verifier per type allowed)",
            example = "1"
    )
    @NotNull(message = "Blockchain configuration ID is required")
    private Long blockchainConfigurationId;
}
