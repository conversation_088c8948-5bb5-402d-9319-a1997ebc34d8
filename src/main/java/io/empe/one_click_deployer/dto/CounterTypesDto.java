package io.empe.one_click_deployer.dto;

import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Types of counters supported by the issuer service.
 *
 * <pre>
 *   issuer.CREDENTIALS_LIMIT          ->  CREDENTIAL
 *   issuer.CREDENTIAL_SCHEMAS_LIMIT   ->  SCHEMA
 *   issuer.REVOCATION_LIST_LIMIT      ->  REVOCATION
 * </pre>
 */
public enum CounterTypesDto {

    CREDENTIAL("issuer.CREDENTIALS_LIMIT"),
    SCHEMA   ("issuer.CREDENTIAL_SCHEMAS_LIMIT"),
    REVOCATION("issuer.REVOCATION_LIST_LIMIT");

    private final String issuerKey;

    CounterTypesDto(String issuerKey) {
        this.issuerKey = issuerKey;
    }

    /** The raw key the issuer service uses (e.g. {@code issuer.CREDENTIALS_LIMIT}). */
    public String getIssuerKey() {
        return issuerKey;
    }

    /** map "issuer.*" → enum value (immutable, built once at class-load time) */
    private static final Map<String, CounterTypesDto> FROM_ISSUER =
            Collections.unmodifiableMap(
                    Stream.of(values())
                            .collect(Collectors.toMap(CounterTypesDto::getIssuerKey, Function.identity()))
            );

    /**
     * Resolve a key such as {@code issuer.CREDENTIALS_LIMIT}.
     *
     * @return the corresponding enum, or {@link Optional#empty()} if the key is unknown.
     */
    public static Optional<CounterTypesDto> fromIssuerKey(String key) {
        return Optional.ofNullable(FROM_ISSUER.get(key));
    }

    /**
     * Convenience method that throws if the key is invalid.
     * Use this when an unknown key is a programming error.
     */
    public static CounterTypesDto require(String key) {
        return fromIssuerKey(key)
                .orElseThrow(() -> new IllegalArgumentException("Unsupported issuer key: " + key));
    }
}