package io.empe.one_click_deployer.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

@Data
public class CreateVerifierVersionRequest {

    @NotBlank(message = "Label cannot be blank")
    private String label;

    @Pattern(regexp = "^\\d+\\.\\d+\\.\\d+$", message = "Version must be in the format x.x.x")
    private String version;

    private String chartVersion;
} 