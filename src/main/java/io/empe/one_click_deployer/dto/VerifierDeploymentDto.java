package io.empe.one_click_deployer.dto;

import io.empe.one_click_deployer.entity.DeploymentType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VerifierDeploymentDto {

    @Schema(description = "Unique identifier of the verifier deployment.",
            example = "1")
    private UUID id;

    @NotBlank(message = "Verifier name is required")
    @Schema(description = "The name of the verifier, e.g., 'VeriTech'. This is a required field.",
            example = "VeriTech")
    private String verifierName;

    @NotBlank(message = "Verifier readable name is required")
    @Schema(description = "A human-readable name for the verifier that can be displayed in UIs and logs.",
            example = "BigStar Verifier Service")
    private String verifierReadableName;


    @NotBlank(message = "Full host is required")
    @Schema(description = "The full host URL associated with the deployment. This is a required field.",
            example = "https://verifier-host.com")
    private String fullHost;

    @Schema(description = "The status of the deployment, such as INIT, PENDING, FAILED, or ACTIVE.",
            example = "ACTIVE")
    private String status;

    @Schema(description = "ID of the user who owns this deployment", example = "10")
    private UUID userId;

    @Schema(description = "The type of deployment. The options are 'EMPE_MOCK_DEPLOYMENT' or 'EMPE_OVH_K8S_DEPLOYMENT'.",
            example = "EMPE_MOCK_DEPLOYMENT")
    private DeploymentType deploymentType;

    @Schema(description = "Version string of the verifier deployment.",
            example = "v1.0.0")
    private String version;


    @Schema(description = "Network name",
            example = "Testnet")
    private String networkName;
}
