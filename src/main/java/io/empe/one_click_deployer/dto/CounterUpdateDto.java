package io.empe.one_click_deployer.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CounterUpdateDto {
    private String name;
    private Integer cyclicalLimit;
    private Boolean resetUsed;
    private Integer additionalLimit;
}