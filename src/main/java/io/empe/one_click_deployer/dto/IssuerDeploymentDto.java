package io.empe.one_click_deployer.dto;

import io.empe.one_click_deployer.entity.DeploymentType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class IssuerDeploymentDto {

    @Schema(description = "Unique identifier of the issuer deployment.",
            example = "1")
    private UUID id;

    @NotBlank(message = "Issuer name is required")
    @Schema(description = "The name of the issuer, e.g., 'IKO'. This is a required field.",
            example = "IKO")
    private String issuerName;

    @NotBlank(message = "Issuer readable name is required")
    @Schema(description = "A human-readable name for the issuer that can be displayed in UIs and logs.",
            example = "BigStar Issuer Service")
    private String issuerReadableName;

    @NotBlank(message = "Full host is required")
    @Schema(description = "The full host URL associated with the deployment. This is a required field.",
            example = "https://issuer-host.com")
    private String fullHost;

    @Schema(description = "The status of the deployment, such as INIT, PENDING, FAILED, or ACTIVE.",
            example = "ACTIVE")
    private String status;

    @Schema(description = "ID of the user who owns this deployment", example = "10")
    private UUID userId;

    @Schema(description = "The type of deployment. The options are 'EMPE_MOCK_DEPLOYMENT' or 'EMPE_OVH_K8S_DEPLOYMENT'.",
            example = "EMPE_MOCK_DEPLOYMENT")
    private DeploymentType deploymentType;

    @NotBlank(message = "Organization name is required")
    @Schema(description = "The organization name associated with the verifier, e.g., 'OrgName'. This is a required field.",
            example = "OrgName")
    private String organization;

    @Schema(description = "Version string of the verifier deployment.",
            example = "v1.0.0")
    private String version;

    @Schema(description = "Network name",
            example = "Testnet")
    private String networkName;

    @Schema(description = "Address of the issuer creator.",
            example = "empe19xu3y3ma2w6tsg9efncuc63u46pn7yrnusp0xc")
    private String issuerCreatorAddress;

    @Schema(description = "DID Document in JSON format as a string.",
            example = "{\"@context\": \"https://www.w3.org/ns/did/v1\", \"id\": \"did:example:123456789abcdefghi\"}")
    private String didDocument;
}
