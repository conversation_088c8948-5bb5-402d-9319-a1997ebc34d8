package io.empe.one_click_deployer.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateUserRequest {

    @NotBlank(message = "Email is required")
    @Email(message = "Email should be valid")
    @Schema(description = "The email of the user. It is required for registration.",
            example = "<EMAIL>")
    private String email;

    @NotBlank(message = "Password is required")
    @Size(min = 8, message = "Password should be at least 8 characters")
    @Schema(description = "The password for the user. It is required for registration.",
            example = "password123")
    private String password;

    @NotBlank(message = "First name is required")
    @Schema(description = "The first name of the user. It is required for registration.",
            example = "John")
    private String firstName;

    @NotBlank(message = "Last name is required")
    @Schema(description = "The last name of the user. It is required for registration.",
            example = "Doe")
    private String lastName;

    @NotBlank(message = "Organization name is required")
    @Schema(description = "The name of the organization. It is required for registration.",
            example = "Example Corp")
    private String organizationName;

    @NotBlank(message = "Phone number is required")
    @Pattern(regexp = "^\\+?[0-9. ()-]{7,25}$", message = "Phone number is invalid")
    @Schema(description = "The phone number of the user. It is required for registration.",
            example = "+1234567890")
    private String phoneNumber;
}
