package io.empe.one_click_deployer.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserDto {
    private UUID id;
    private String email;
    private String firstName;
    private String lastName;
    private String organizationName;
    private boolean subscriptionActive;
    private List<MetadataDto> metadata;
    private String phoneNumber;
}
