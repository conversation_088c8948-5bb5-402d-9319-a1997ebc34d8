package io.empe.one_click_deployer.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatus;

/**
 * Epoka Error
 * This is a POJO of Epoka Error class.
 *
 * <pre>
 * - uuid- request ID that comes or generated if missing.
 * - code- defined in exception.
 * - message- defined in exception.
 * - httpStatus- defined in exception.
 * </pre>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ErrorResponseDto {
    //    private String uuid;
    private long code;
    private String message;
    private HttpStatus httpStatus;
}
