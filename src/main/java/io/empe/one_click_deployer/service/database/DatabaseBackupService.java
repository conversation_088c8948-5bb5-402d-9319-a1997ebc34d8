package io.empe.one_click_deployer.service.database;

import io.empe.one_click_deployer.entity.IssuerDeployment;
import io.empe.one_click_deployer.entity.VerifierDeployment;
import io.empe.one_click_deployer.entity.User;
import io.empe.one_click_deployer.exception.external.DeploymentNotFound;
import io.empe.one_click_deployer.exception.external.PermissionDeniedException;
import io.empe.one_click_deployer.repository.IssuerDeploymentRepository;
import io.empe.one_click_deployer.repository.VerifierDeploymentRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.services.s3.S3Configuration;

import java.net.URI;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class DatabaseBackupService {

    private final IssuerDeploymentRepository issuerDeploymentRepository;
    private final VerifierDeploymentRepository verifierDeploymentRepository;

    @Value("${spring.datasource.url}")
    private String datasourceUrl;

    @Value("${spring.datasource.username}")
    private String adminUsername;

    @Value("${spring.datasource.password}")
    private String adminPassword;

    @Value("${backup.directory:/tmp/backups}")
    private String backupDirectory;

    @Value("${backup.s3.bucket:}")
    private String s3Bucket;

    @Value("${backup.s3.region:eu-west-1}")
    private String s3Region;

    @Value("${backup.s3.endpoint:}")
    private String s3Endpoint;

    @Value("${backup.s3.accessKey:}")
    private String s3AccessKey;

    @Value("${backup.s3.secretKey:}")
    private String s3SecretKey;

    /**
     * Scheduled task to backup all issuer and verifier databases daily at 2 AM
     */
    @Scheduled(cron = "0 25 11 * * ?")
    public void scheduleDailyBackups() {
        log.info("Starting scheduled daily database backups");

        // Create backup directory if it doesn't exist
        createBackupDirectory();

        // Backup issuer databases
        backupIssuerDatabases();

        // Backup verifier databases
        backupVerifierDatabases();

        log.info("Completed scheduled daily database backups");
    }

    /**
     * Backup all issuer databases
     */
    public void backupIssuerDatabases() {
        List<IssuerDeployment> issuers = issuerDeploymentRepository.findAll();
        log.info("Starting backup of {} issuer databases", issuers.size());

        if (issuers.isEmpty()) {
            log.warn("No issuer deployments found to backup");
            return;
        }

        for (IssuerDeployment issuer : issuers) {
            try {
                log.debug("Processing backup for issuer: {} (ID: {})", issuer.getIssuerName(), issuer.getId());
                backupIssuerDatabase(issuer);
                log.debug("Successfully completed backup for issuer: {}", issuer.getIssuerName());
            } catch (Exception e) {
                log.error("Failed to backup issuer database for {}: {}", issuer.getIssuerName(), e.getMessage(), e);
            }
        }
        log.info("Completed backup process for all issuer databases");
    }

    /**
     * Backup all verifier databases
     */
    public void backupVerifierDatabases() {
        List<VerifierDeployment> verifiers = verifierDeploymentRepository.findAll();
        log.info("Starting backup of {} verifier databases", verifiers.size());

        if (verifiers.isEmpty()) {
            log.warn("No verifier deployments found to backup");
            return;
        }

        for (VerifierDeployment verifier : verifiers) {
            try {
                log.debug("Processing backup for verifier: {} (ID: {})", verifier.getVerifierName(), verifier.getId());
                backupVerifierDatabase(verifier);
                log.debug("Successfully completed backup for verifier: {}", verifier.getVerifierName());
            } catch (Exception e) {
                log.error("Failed to backup verifier database for {}: {}", verifier.getVerifierName(), e.getMessage(), e);
            }
        }
        log.info("Completed backup process for all verifier databases");
    }

    /**
     * Backup a specific issuer database
     */
    public void backupIssuerDatabase(IssuerDeployment issuer) throws IOException, InterruptedException, SQLException {
        log.info("Backing up database for issuer: {}", issuer.getIssuerName());

        // Generate database name based on the same pattern used in PostgreSQLInitializer
        String sanitizedDeploymentName = issuer.getIssuerName().replaceAll("[^a-zA-Z0-9_]", "_");

        // We don't have the exact username, but we can list databases and find the one matching the pattern
        String databasePattern = "db_" + sanitizedDeploymentName + "_";

        // Get database connection info
        DatabaseConnectionInfo dbInfo = parseJdbcUrl(datasourceUrl);

        // List databases matching the pattern
        log.debug("Searching for databases with pattern: {}", databasePattern);
        List<String> databases = listDatabases(dbInfo, databasePattern);
        log.debug("Found {} databases matching pattern for issuer {}: {}", databases.size(), issuer.getIssuerName(), databases);

        if (databases.isEmpty()) {
            log.warn("No database found for issuer: {} with pattern: {}", issuer.getIssuerName(), databasePattern);
            return;
        }

        // Use the first matching database
        String databaseName = databases.get(0);
        log.info("Found database for issuer {}: {}", issuer.getIssuerName(), databaseName);

        // Create backup
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String backupFileName = String.format("issuer_%s_%s.sql", sanitizedDeploymentName, timestamp);
        String backupFilePath = Paths.get(backupDirectory, backupFileName).toString();

        executeBackup(dbInfo.host, dbInfo.port, databaseName, backupFilePath);

        // Upload to S3 if bucket is configured
        if (s3Bucket != null && !s3Bucket.isEmpty()) {
            boolean uploadSuccess = uploadToS3(backupFilePath, "issuers/" + backupFileName);

            // Delete local backup file after successful S3 upload
            if (uploadSuccess) {
                try {
                    Files.delete(Paths.get(backupFilePath));
                    log.info("Local backup file deleted after successful S3 upload: {}", backupFilePath);
                } catch (IOException e) {
                    log.warn("Failed to delete local backup file after S3 upload: {}", backupFilePath, e);
                }
            } else {
                log.warn("S3 upload failed, keeping local backup file: {}", backupFilePath);
            }
        }

        log.info("Backup completed for issuer: {}", issuer.getIssuerName());
    }

    /**
     * Backup a specific verifier database
     */
    public void backupVerifierDatabase(VerifierDeployment verifier) throws IOException, InterruptedException, SQLException {
        log.info("Backing up database for verifier: {}", verifier.getVerifierName());

        // Generate database name based on the same pattern used in PostgreSQLInitializer
        String sanitizedDeploymentName = verifier.getVerifierName().replaceAll("[^a-zA-Z0-9_]", "_");

        // We don't have the exact username, but we can list databases and find the one matching the pattern
        String databasePattern = "db_" + sanitizedDeploymentName + "_";

        // Get database connection info
        DatabaseConnectionInfo dbInfo = parseJdbcUrl(datasourceUrl);

        // List databases matching the pattern
        log.debug("Searching for databases with pattern: {}", databasePattern);
        List<String> databases = listDatabases(dbInfo, databasePattern);
        log.debug("Found {} databases matching pattern for verifier {}: {}", databases.size(), verifier.getVerifierName(), databases);

        if (databases.isEmpty()) {
            log.warn("No database found for verifier: {} with pattern: {}", verifier.getVerifierName(), databasePattern);
            return;
        }

        // Use the first matching database
        String databaseName = databases.get(0);
        log.info("Found database for verifier {}: {}", verifier.getVerifierName(), databaseName);

        // Create backup
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String backupFileName = String.format("verifier_%s_%s.sql", sanitizedDeploymentName, timestamp);
        String backupFilePath = Paths.get(backupDirectory, backupFileName).toString();

        executeBackup(dbInfo.host, dbInfo.port, databaseName, backupFilePath);

        // Upload to S3 if bucket is configured
        if (s3Bucket != null && !s3Bucket.isEmpty()) {
            boolean uploadSuccess = uploadToS3(backupFilePath, "verifiers/" + backupFileName);

            // Delete local backup file after successful S3 upload
            if (uploadSuccess) {
                try {
                    Files.delete(Paths.get(backupFilePath));
                    log.info("Local backup file deleted after successful S3 upload: {}", backupFilePath);
                } catch (IOException e) {
                    log.warn("Failed to delete local backup file after S3 upload: {}", backupFilePath, e);
                }
            } else {
                log.warn("S3 upload failed, keeping local backup file: {}", backupFilePath);
            }
        }

        log.info("Backup completed for verifier: {}", verifier.getVerifierName());
    }

    /**
     * Backup a specific issuer database by ID with user authorization
     */
    public void backupIssuerById(UUID issuerId, User user) throws DeploymentNotFound, PermissionDeniedException, IOException, InterruptedException, SQLException {
        log.info("Backing up issuer database for ID: {} by user: {}", issuerId, user.getId());

        // Find the issuer deployment
        IssuerDeployment issuer = issuerDeploymentRepository.findById(issuerId)
                .orElseThrow(() -> new DeploymentNotFound("Issuer deployment not found"));

        // Check if user has permission to backup this deployment
        if (!issuer.getUser().getId().equals(user.getId()) && !user.getRole().equals(User.Role.ADMIN)) {
            throw new PermissionDeniedException("You do not have permission to backup this deployment");
        }

        // Perform the backup
        backupIssuerDatabase(issuer);
    }

    /**
     * Backup a specific verifier database by ID with user authorization
     */
    public void backupVerifierById(UUID verifierId, User user) throws DeploymentNotFound, PermissionDeniedException, IOException, InterruptedException, SQLException {
        log.info("Backing up verifier database for ID: {} by user: {}", verifierId, user.getId());

        // Find the verifier deployment
        VerifierDeployment verifier = verifierDeploymentRepository.findById(verifierId)
                .orElseThrow(() -> new DeploymentNotFound("Verifier deployment not found"));

        // Check if user has permission to backup this deployment
        if (!verifier.getUser().getId().equals(user.getId()) && !user.getRole().equals(User.Role.ADMIN)) {
            throw new PermissionDeniedException("You do not have permission to backup this deployment");
        }

        // Perform the backup
        backupVerifierDatabase(verifier);
    }

    /**
     * Execute database backup using pg_dump
     */
    private void executeBackup(String host, String port, String databaseName, String outputFile) throws IOException, InterruptedException {
        log.info("Starting database backup for {} to {}", databaseName, outputFile);

        ProcessBuilder processBuilder = new ProcessBuilder(
                "pg_dump",
                "-h", host,
                "-p", port,
                "-U", adminUsername,
                "-d", databaseName,
                "--clean",
                "--create",
                "--if-exists",
                "--verbose",
                "-f", outputFile
        );

        // Set PGPASSWORD environment variable
        processBuilder.environment().put("PGPASSWORD", adminPassword);

        // Redirect error stream to output stream
        processBuilder.redirectErrorStream(true);

        // Start the process
        Process process = processBuilder.start();

        // Read the output
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                log.debug("pg_dump: {}", line);
            }
        }

        // Wait for the process to complete
        boolean completed = process.waitFor(10, TimeUnit.MINUTES);

        if (!completed) {
            process.destroyForcibly();
            throw new IOException("Backup process timed out after 10 minutes");
        }

        int exitCode = process.exitValue();
        if (exitCode != 0) {
            throw new IOException("Backup process failed with exit code: " + exitCode);
        }

        // Check if the output file exists and has content
        File backupFile = new File(outputFile);
        if (!backupFile.exists() || backupFile.length() == 0) {
            throw new IOException("Backup file was not created or is empty: " + outputFile);
        }

        log.info("Backup file created successfully: {}", outputFile);
    }

    /**
     * Restore a specific issuer database from a backup file
     */
    public void restoreIssuerDatabase(UUID issuerId, String backupFileName, User user) throws DeploymentNotFound, PermissionDeniedException, IOException, InterruptedException, SQLException {
        log.info("Restoring issuer database for ID: {} from backup: {} by user: {}", issuerId, backupFileName, user.getId());

        // Find the issuer deployment
        IssuerDeployment issuer = issuerDeploymentRepository.findById(issuerId)
                .orElseThrow(() -> new DeploymentNotFound("Issuer deployment not found"));

        // Check if user has permission to restore this deployment
        if (!issuer.getUser().getId().equals(user.getId()) && !user.getRole().equals(User.Role.ADMIN)) {
            throw new PermissionDeniedException("You do not have permission to restore this deployment");
        }

        // Verify that the backup file belongs to this issuer
        if (!canUserAccessBackup(user, backupFileName)) {
            throw new PermissionDeniedException("You do not have permission to access this backup file");
        }

        // Get database connection info
        DatabaseConnectionInfo dbInfo = parseJdbcUrl(datasourceUrl);

        // Find the target database
        String sanitizedDeploymentName = issuer.getIssuerName().replaceAll("[^a-zA-Z0-9_]", "_");
        String databasePattern = "db_" + sanitizedDeploymentName + "_";
        List<String> databases = listDatabases(dbInfo, databasePattern);

        if (databases.isEmpty()) {
            throw new RuntimeException("No database found for issuer: " + issuer.getIssuerName());
        }

        String targetDatabase = databases.get(0);
        log.info("Target database for restore: {}", targetDatabase);

        // Perform the restore
        executeRestore(dbInfo.host, dbInfo.port, targetDatabase, backupFileName);

        log.info("Database restore completed for issuer: {}", issuer.getIssuerName());
    }

    /**
     * Restore a specific verifier database from a backup file
     */
    public void restoreVerifierDatabase(UUID verifierId, String backupFileName, User user) throws DeploymentNotFound, PermissionDeniedException, IOException, InterruptedException, SQLException {
        log.info("Restoring verifier database for ID: {} from backup: {} by user: {}", verifierId, backupFileName, user.getId());

        // Find the verifier deployment
        VerifierDeployment verifier = verifierDeploymentRepository.findById(verifierId)
                .orElseThrow(() -> new DeploymentNotFound("Verifier deployment not found"));

        // Check if user has permission to restore this deployment
        if (!verifier.getUser().getId().equals(user.getId()) && !user.getRole().equals(User.Role.ADMIN)) {
            throw new PermissionDeniedException("You do not have permission to restore this deployment");
        }

        // Verify that the backup file belongs to this verifier
        if (!canUserAccessBackup(user, backupFileName)) {
            throw new PermissionDeniedException("You do not have permission to access this backup file");
        }

        // Get database connection info
        DatabaseConnectionInfo dbInfo = parseJdbcUrl(datasourceUrl);

        // Find the target database
        String sanitizedDeploymentName = verifier.getVerifierName().replaceAll("[^a-zA-Z0-9_]", "_");
        String databasePattern = "db_" + sanitizedDeploymentName + "_";
        List<String> databases = listDatabases(dbInfo, databasePattern);

        if (databases.isEmpty()) {
            throw new RuntimeException("No database found for verifier: " + verifier.getVerifierName());
        }

        String targetDatabase = databases.get(0);
        log.info("Target database for restore: {}", targetDatabase);

        // Perform the restore
        executeRestore(dbInfo.host, dbInfo.port, targetDatabase, backupFileName);

        log.info("Database restore completed for verifier: {}", verifier.getVerifierName());
    }

    /**
     * Execute database restore from a backup file
     */
    private void executeRestore(String host, String port, String databaseName, String backupFileName) throws IOException, InterruptedException {
        log.info("Starting database restore for {} from backup: {}", databaseName, backupFileName);

        // Create backup directory if it doesn't exist
        createBackupDirectory();

        // Download backup file from S3 to local temporary file
        String localBackupFile = downloadBackupToLocal(backupFileName);

        try {
            ProcessBuilder processBuilder = new ProcessBuilder(
                    "psql",
                    "-h", host,
                    "-p", port,
                    "-U", adminUsername,
                    "-d", databaseName,
                    "-f", localBackupFile
            );

            // Set PGPASSWORD environment variable
            processBuilder.environment().put("PGPASSWORD", adminPassword);

            // Redirect error stream to output stream
            processBuilder.redirectErrorStream(true);

            // Start the process
            Process process = processBuilder.start();

            // Read the output
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    log.debug("psql: {}", line);
                }
            }

            // Wait for the process to complete
            boolean completed = process.waitFor(15, TimeUnit.MINUTES);

            if (!completed) {
                process.destroyForcibly();
                throw new IOException("Restore process timed out after 15 minutes");
            }

            int exitCode = process.exitValue();
            if (exitCode != 0) {
                throw new IOException("Restore process failed with exit code: " + exitCode);
            }

            log.info("Database restore completed successfully for: {}", databaseName);
        } finally {
            // Clean up the temporary backup file
            try {
                Files.delete(Paths.get(localBackupFile));
                log.debug("Deleted temporary backup file: {}", localBackupFile);
            } catch (IOException e) {
                log.warn("Failed to delete temporary backup file: {}", localBackupFile, e);
            }
        }
    }

    /**
     * Download backup file from S3 to a local temporary file
     */
    private String downloadBackupToLocal(String backupFileName) throws IOException {
        log.info("Downloading backup file from S3: {}", backupFileName);

        String localFileName = "restore_" + System.currentTimeMillis() + "_" + 
                               backupFileName.substring(backupFileName.lastIndexOf("/") + 1);
        String localFilePath = Paths.get(backupDirectory, localFileName).toString();

        try (ResponseInputStream<GetObjectResponse> s3Object = downloadBackup(backupFileName);
             FileOutputStream fos = new FileOutputStream(localFilePath)) {

            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = s3Object.read(buffer)) != -1) {
                fos.write(buffer, 0, bytesRead);
            }

            log.info("Downloaded backup file to: {}", localFilePath);
            return localFilePath;
        } catch (Exception e) {
            log.error("Failed to download backup file from S3: {}", backupFileName, e);
            throw new IOException("Failed to download backup file: " + e.getMessage(), e);
        }
    }

    /**
     * List databases matching a pattern using JDBC
     */
    private List<String> listDatabases(DatabaseConnectionInfo dbInfo, String pattern) throws SQLException {
        List<String> databases = new ArrayList<>();

        // Connect to the PostgreSQL server (using postgres database)
        String url = String.format("********************************", dbInfo.host, dbInfo.port);

        try (Connection conn = DriverManager.getConnection(url, adminUsername, adminPassword);
             PreparedStatement stmt = conn.prepareStatement("SELECT datname FROM pg_database WHERE datname LIKE ?")) {

            stmt.setString(1, pattern + "%");

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    String dbName = rs.getString("datname");
                    databases.add(dbName);
                }
            }
        }

        return databases;
    }


    /**
     * Create backup directory if it doesn't exist
     */
    private void createBackupDirectory() {
        Path directory = Paths.get(backupDirectory);
        if (!Files.exists(directory)) {
            try {
                Files.createDirectories(directory);
                log.info("Created backup directory: {}", backupDirectory);
            } catch (IOException e) {
                log.error("Failed to create backup directory: {}", backupDirectory, e);
            }
        }
    }

    /**
     * Parse JDBC URL to extract host, port, and database
     */
    private DatabaseConnectionInfo parseJdbcUrl(String jdbcUrl) {
        try {
            String cleanUrl = jdbcUrl.replace("jdbc:", "");
            java.net.URI uri = new java.net.URI(cleanUrl);
            String host = uri.getHost();
            int port = uri.getPort();
            String database = uri.getPath().replaceFirst("/", "");
            return new DatabaseConnectionInfo(host, String.valueOf(port), database);
        } catch (java.net.URISyntaxException e) {
            throw new RuntimeException("Invalid JDBC URL: " + jdbcUrl, e);
        }
    }

    /**
     * Upload a file to S3
     * @param filePath Path to the local file to upload
     * @param s3Key S3 key for the uploaded file
     * @return true if upload was successful, false otherwise
     */
    private boolean uploadToS3(String filePath, String s3Key) {
        log.info("Uploading file {} to S3 bucket {} with key {}", filePath, s3Bucket, s3Key);

        try {
            S3Client s3Client = createS3Client();
            log.debug("S3 client created successfully for upload");

            // Create request
            PutObjectRequest request = PutObjectRequest.builder()
                    .bucket(s3Bucket)
                    .key(s3Key)
                    .build();

            log.debug("Uploading file to S3 - bucket: {}, key: {}, file size: {} bytes", 
                s3Bucket, s3Key, new File(filePath).length());

            // Upload file
            s3Client.putObject(request, RequestBody.fromFile(new File(filePath)));

            log.info("Successfully uploaded file to S3: s3://{}/{}", s3Bucket, s3Key);
            return true;
        } catch (Exception e) {
            log.error("Failed to upload file to S3 - bucket: {}, key: {}, error: {}", s3Bucket, s3Key, e.getMessage(), e);
            return false;
        }
    }


    /**
     * List all backup files from S3
     * @return List of backup file information
     */
    public List<BackupFileInfo> listBackups() {
        log.info("Listing backups from S3 bucket: {}", s3Bucket);

        if (s3Bucket == null || s3Bucket.isEmpty()) {
            log.warn("S3 bucket not configured, cannot list backups");
            return List.of();
        }

        try {
            S3Client s3Client = createS3Client();
            log.debug("S3 client created successfully");

            ListObjectsV2Request request = ListObjectsV2Request.builder()
                    .bucket(s3Bucket)
                    .build();

            log.debug("Making ListObjectsV2 request to bucket: {}", s3Bucket);
            ListObjectsV2Response response = s3Client.listObjectsV2(request);

            log.info("S3 ListObjectsV2 response received. Object count: {}", response.contents().size());
            log.debug("S3 response details - IsTruncated: {}, KeyCount: {}", response.isTruncated(), response.keyCount());

            // Log each object found
            response.contents().forEach(obj -> 
                log.debug("Found S3 object: {} (size: {}, lastModified: {})", obj.key(), obj.size(), obj.lastModified())
            );

            List<BackupFileInfo> backupFiles = response.contents().stream()
                    .map(this::mapToBackupFileInfo)
                    .sorted((a, b) -> b.lastModified().compareTo(a.lastModified())) // Sort by last modified descending
                    .toList();

            log.info("Mapped {} S3 objects to backup file info", backupFiles.size());
            backupFiles.forEach(backup -> 
                log.debug("Backup file: {} (type: {}, deployment: {})", backup.fileName(), backup.type(), backup.deploymentName())
            );

            return backupFiles;

        } catch (Exception e) {
            log.error("Failed to list backups from S3: {}", e.getMessage(), e);
            return List.of();
        }
    }

    /**
     * List backup files from S3 filtered by user ownership
     * @param user The user to filter backups for
     * @return List of backup file information for deployments owned by the user
     */
    public List<BackupFileInfo> listBackupsForUser(io.empe.one_click_deployer.entity.User user) {
        log.info("Listing backups for user: {}", user.getId());

        // Get all backups
        List<BackupFileInfo> allBackups = listBackups();
        log.debug("Retrieved {} total backups from S3", allBackups.size());

        // Get user's deployment names
        Set<String> userDeploymentNames = getUserDeploymentNames(user);
        log.debug("User {} has {} deployment names: {}", user.getId(), userDeploymentNames.size(), userDeploymentNames);

        // Filter backups by user's deployments
        List<BackupFileInfo> filteredBackups = allBackups.stream()
                .filter(backup -> {
                    boolean matches = userDeploymentNames.contains(backup.deploymentName());
                    log.debug("Backup {} (deployment: {}) matches user deployments: {}", backup.fileName(), backup.deploymentName(), matches);
                    return matches;
                })
                .toList();

        log.info("Filtered to {} backups for user {}", filteredBackups.size(), user.getId());
        return filteredBackups;
    }

    /**
     * Check if a user can access a specific backup file
     * @param user The user requesting access
     * @param fileName The backup file name
     * @return true if the user can access the backup, false otherwise
     */
    public boolean canUserAccessBackup(io.empe.one_click_deployer.entity.User user, String fileName) {
        // Admins can access all backups
        if (user.getRole() == io.empe.one_click_deployer.entity.User.Role.ADMIN) {
            return true;
        }

        // Get user's deployment names
        Set<String> userDeploymentNames = getUserDeploymentNames(user);

        // Extract deployment name from file name
        String deploymentName = extractDeploymentNameFromFileName(fileName);

        return userDeploymentNames.contains(deploymentName);
    }

    /**
     * Get all deployment names owned by a user
     * @param user The user
     * @return Set of deployment names (sanitized names used in backup files)
     */
    private Set<String> getUserDeploymentNames(io.empe.one_click_deployer.entity.User user) {
        Set<String> deploymentNames = issuerDeploymentRepository.findByUserId(user.getId())
                .stream()
                .map(issuer -> issuer.getIssuerName().replaceAll("[^a-zA-Z0-9_]", "_"))
                .collect(Collectors.toSet());

        deploymentNames.addAll(verifierDeploymentRepository.findByUserId(user.getId())
                .stream()
                .map(verifier -> verifier.getVerifierName().replaceAll("[^a-zA-Z0-9_]", "_"))
                .collect(Collectors.toSet()));

        return deploymentNames;
    }

    /**
     * Extract deployment name from backup file name
     * @param fileName The backup file name (e.g., "issuers/issuer_deploymentname_timestamp.sql")
     * @return The deployment name or "unknown" if not found
     */
    private String extractDeploymentNameFromFileName(String fileName) {
        if (fileName.startsWith("issuers/")) {
            String actualFileName = fileName.substring("issuers/".length());
            if (actualFileName.startsWith("issuer_")) {
                String[] parts = actualFileName.split("_");
                if (parts.length >= 3) {
                    return parts[1];
                }
            }
        } else if (fileName.startsWith("verifiers/")) {
            String actualFileName = fileName.substring("verifiers/".length());
            if (actualFileName.startsWith("verifier_")) {
                String[] parts = actualFileName.split("_");
                if (parts.length >= 3) {
                    return parts[1];
                }
            }
        }
        return "unknown";
    }

    /**
     * List backup files from S3 filtered by specific issuer ID
     * @param issuerId The issuer ID to filter backups for
     * @param user The user requesting the backups
     * @return List of backup file information for the specific issuer
     */
    public List<BackupFileInfo> listBackupsForIssuer(UUID issuerId, io.empe.one_click_deployer.entity.User user) throws DeploymentNotFound, PermissionDeniedException {
        log.info("Listing backups for issuer ID: {} by user: {}", issuerId, user.getId());

        // Find the issuer deployment
        IssuerDeployment issuer = issuerDeploymentRepository.findById(issuerId)
                .orElseThrow(() -> new DeploymentNotFound("Issuer deployment not found"));
        log.info("Found issuer deployment: {} (name: {})", issuer.getId(), issuer.getIssuerName());

        // Check if user has permission to view this deployment
        if (!issuer.getUser().getId().equals(user.getId()) && !user.getRole().equals(User.Role.ADMIN)) {
            throw new PermissionDeniedException("You do not have permission to view backups for this deployment");
        }

        // Get all backups
        List<BackupFileInfo> allBackups = listBackups();
        log.info("Retrieved {} total backups from S3 for issuer filtering", allBackups.size());

        // Log all backup files for debugging
        log.info("All backup files found:");
        allBackups.forEach(backup -> 
            log.info("  - File: {}, Type: {}, Deployment: {}", backup.fileName(), backup.type(), backup.deploymentName())
        );

        // Get the sanitized deployment name for this issuer
        String sanitizedDeploymentName = issuer.getIssuerName().replaceAll("[^a-zA-Z0-9_]", "_");
        log.info("Sanitized deployment name for issuer {}: {}", issuer.getIssuerName(), sanitizedDeploymentName);

        // Filter backups by this specific issuer
        List<BackupFileInfo> filteredBackups = allBackups.stream()
                .filter(backup -> {
                    boolean typeMatches = "issuer".equals(backup.type());
                    boolean nameMatches = sanitizedDeploymentName.equals(backup.deploymentName());
                    boolean matches = typeMatches && nameMatches;
                    log.debug("Backup {} - type: {} (matches: {}), deployment: {} (matches: {}), overall match: {}", 
                        backup.fileName(), backup.type(), typeMatches, backup.deploymentName(), nameMatches, matches);
                    return matches;
                })
                .toList();

        log.info("Filtered to {} backups for issuer ID: {}", filteredBackups.size(), issuerId);
        return filteredBackups;
    }

    /**
     * List backup files from S3 filtered by specific verifier ID
     * @param verifierId The verifier ID to filter backups for
     * @param user The user requesting the backups
     * @return List of backup file information for the specific verifier
     */
    public List<BackupFileInfo> listBackupsForVerifier(UUID verifierId, io.empe.one_click_deployer.entity.User user) throws DeploymentNotFound, PermissionDeniedException {
        log.info("Listing backups for verifier ID: {} by user: {}", verifierId, user.getId());

        // Find the verifier deployment
        VerifierDeployment verifier = verifierDeploymentRepository.findById(verifierId)
                .orElseThrow(() -> new DeploymentNotFound("Verifier deployment not found"));
        log.debug("Found verifier deployment: {} (name: {})", verifier.getId(), verifier.getVerifierName());

        // Check if user has permission to view this deployment
        if (!verifier.getUser().getId().equals(user.getId()) && !user.getRole().equals(User.Role.ADMIN)) {
            throw new PermissionDeniedException("You do not have permission to view backups for this deployment");
        }

        // Get all backups
        List<BackupFileInfo> allBackups = listBackups();
        log.debug("Retrieved {} total backups from S3 for verifier filtering", allBackups.size());

        // Get the sanitized deployment name for this verifier
        String sanitizedDeploymentName = verifier.getVerifierName().replaceAll("[^a-zA-Z0-9_]", "_");
        log.debug("Sanitized deployment name for verifier {}: {}", verifier.getVerifierName(), sanitizedDeploymentName);

        // Filter backups by this specific verifier
        List<BackupFileInfo> filteredBackups = allBackups.stream()
                .filter(backup -> {
                    boolean typeMatches = "verifier".equals(backup.type());
                    boolean nameMatches = sanitizedDeploymentName.equals(backup.deploymentName());
                    boolean matches = typeMatches && nameMatches;
                    log.debug("Backup {} - type: {} (matches: {}), deployment: {} (matches: {}), overall match: {}", 
                        backup.fileName(), backup.type(), typeMatches, backup.deploymentName(), nameMatches, matches);
                    return matches;
                })
                .toList();

        log.info("Filtered to {} backups for verifier ID: {}", filteredBackups.size(), verifierId);
        return filteredBackups;
    }

    /**
     * Download a specific backup file from S3
     * @param fileName The name of the backup file to download
     * @return InputStream of the backup file
     */
    public ResponseInputStream<GetObjectResponse> downloadBackup(String fileName) {
        log.info("Downloading backup file: {} from S3 bucket: {}", fileName, s3Bucket);

        if (s3Bucket == null || s3Bucket.isEmpty()) {
            throw new RuntimeException("S3 bucket not configured");
        }

        try {
            S3Client s3Client = createS3Client();

            GetObjectRequest request = GetObjectRequest.builder()
                    .bucket(s3Bucket)
                    .key(fileName)
                    .build();

            return s3Client.getObject(request);

        } catch (Exception e) {
            log.error("Failed to download backup file {} from S3: {}", fileName, e.getMessage(), e);
            throw new RuntimeException("Failed to download backup file: " + e.getMessage(), e);
        }
    }

    /**
     * Create S3 client with configured settings
     */
    private S3Client createS3Client() {
        // Create S3 client builder
        var s3ClientBuilder = S3Client.builder()
                .region(Region.of(s3Region));

        // Configure custom endpoint if provided
        if (s3Endpoint != null && !s3Endpoint.isEmpty()) {
            s3ClientBuilder.endpointOverride(URI.create(s3Endpoint));

            // For custom endpoints like OVH, enable path-style access
            s3ClientBuilder.serviceConfiguration(S3Configuration.builder()
                    .pathStyleAccessEnabled(true)
                    .build());
        }

        // Configure credentials if provided
        if (s3AccessKey != null && !s3AccessKey.isEmpty() && 
            s3SecretKey != null && !s3SecretKey.isEmpty()) {
            AwsBasicCredentials credentials = AwsBasicCredentials.create(s3AccessKey, s3SecretKey);
            s3ClientBuilder.credentialsProvider(StaticCredentialsProvider.create(credentials));
        }

        return s3ClientBuilder.build();
    }

    /**
     * Map S3Object to BackupFileInfo
     */
    private BackupFileInfo mapToBackupFileInfo(S3Object s3Object) {
        String key = s3Object.key();
        String type = "unknown";
        String deploymentName = "unknown";

        log.debug("Processing S3 object: {}", key);

        // Parse the key to extract type and deployment name
        if (key.startsWith("issuers/")) {
            type = "issuer";
            String fileName = key.substring("issuers/".length());
            log.debug("Processing issuer backup file: {}", fileName);

            if (fileName.startsWith("issuer_")) {
                deploymentName = extractDeploymentNameFromBackupFileName(fileName, "issuer_");
                log.debug("Extracted deployment name '{}' from issuer file '{}'", deploymentName, fileName);
            } else {
                log.warn("Issuer backup file does not start with 'issuer_': {}", fileName);
            }
        } else if (key.startsWith("verifiers/")) {
            type = "verifier";
            String fileName = key.substring("verifiers/".length());
            log.debug("Processing verifier backup file: {}", fileName);

            if (fileName.startsWith("verifier_")) {
                deploymentName = extractDeploymentNameFromBackupFileName(fileName, "verifier_");
                log.debug("Extracted deployment name '{}' from verifier file '{}'", deploymentName, fileName);
            } else {
                log.warn("Verifier backup file does not start with 'verifier_': {}", fileName);
            }
        } else {
            log.debug("S3 object does not match expected backup file pattern: {}", key);
        }

        BackupFileInfo result = new BackupFileInfo(
                key,
                type,
                deploymentName,
                s3Object.size(),
                s3Object.lastModified()
        );

        log.debug("Created BackupFileInfo: fileName={}, type={}, deploymentName={}", 
            result.fileName(), result.type(), result.deploymentName());

        return result;
    }

    /**
     * Extract deployment name from backup file name
     * @param fileName The backup file name (e.g., "issuer_pablo_test_backup_issuer_stg_20250701_142612.sql")
     * @param prefix The prefix to remove (e.g., "issuer_" or "verifier_")
     * @return The deployment name (e.g., "pablo_test_backup_issuer_stg")
     */
    private String extractDeploymentNameFromBackupFileName(String fileName, String prefix) {
        if (!fileName.startsWith(prefix)) {
            return "unknown";
        }

        // Remove the prefix
        String withoutPrefix = fileName.substring(prefix.length());

        // Find the timestamp pattern (8 digits followed by underscore and 6 digits)
        // Pattern: deploymentname_YYYYMMDD_HHMMSS.sql
        String[] parts = withoutPrefix.split("_");

        // Look for the timestamp pattern - should be 8 digits followed by 6 digits
        int timestampIndex = -1;
        for (int i = 0; i < parts.length - 1; i++) {
            if (parts[i].matches("\\d{8}") && i + 1 < parts.length && 
                parts[i + 1].matches("\\d{6}(\\.sql)?")) {
                timestampIndex = i;
                break;
            }
        }

        if (timestampIndex > 0) {
            // Join all parts before the timestamp to get the deployment name
            StringBuilder deploymentName = new StringBuilder();
            for (int i = 0; i < timestampIndex; i++) {
                if (i > 0) {
                    deploymentName.append("_");
                }
                deploymentName.append(parts[i]);
            }
            String result = deploymentName.toString();
            log.debug("Extracted deployment name '{}' from backup file '{}' (timestamp at index {})", result, fileName, timestampIndex);
            return result;
        }

        log.warn("Could not extract deployment name from backup file: {} (no timestamp pattern found)", fileName);
        return "unknown";
    }

    /**
     * Record class for database connection information
     */
    private record DatabaseConnectionInfo(String host, String port, String database) {}

    /**
     * Record class for backup file information
     */
    public record BackupFileInfo(
            String fileName,
            String type,
            String deploymentName,
            Long size,
            java.time.Instant lastModified
    ) {}
}
