package io.empe.one_click_deployer.service.database;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.net.URISyntaxException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.UUID;

@Service
@Slf4j
@RequiredArgsConstructor
public class PostgreSQLInitializer {

    @Value("${spring.datasource.url}")
    private String datasourceUrl;

    @Value("${spring.datasource.username}")
    private String adminUsername;

    @Value("${spring.datasource.password}")
    private String adminPassword;

    public PostgreSQLCredentials initializeDatabase(String deploymentName) {
        String username = "user_" + UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        String password = UUID.randomUUID().toString().replace("-", "").substring(0, 16);
        String sanitizedDeploymentName = deploymentName.replaceAll("[^a-zA-Z0-9_]", "_");
        String databaseName = "db_" + sanitizedDeploymentName + "_" + username;

        DatabaseConnectionInfo adminDbInfo = parseJdbcUrl(datasourceUrl);
        String adminUrl = buildJdbcUrl(adminDbInfo.host, adminDbInfo.port, "postgres");

        log.info("Creating PostgreSQL user '{}' and database '{}'", username, databaseName);

        try (Connection adminConnection = DriverManager.getConnection(adminUrl, adminUsername, adminPassword);
             Statement stmt = adminConnection.createStatement()) {

            stmt.executeUpdate("CREATE USER \"" + username + "\" WITH PASSWORD '" + password + "'");
            stmt.executeUpdate("CREATE DATABASE \"" + databaseName + "\" OWNER \"" + username + "\"");

            log.info("User '{}' created and database '{}' created", username, databaseName);
        } catch (SQLException e) {
            log.error("Failed to create user or database", e);
            throw new RuntimeException("Failed to create user or database", e);
        }

        // Reconnect to new database to fix schema ownership
        String newDbUrl = buildJdbcUrl(adminDbInfo.host, adminDbInfo.port, databaseName);

        try (Connection dbConnection = DriverManager.getConnection(newDbUrl, adminUsername, adminPassword);
             Statement stmt = dbConnection.createStatement()) {

            stmt.executeUpdate("ALTER SCHEMA public OWNER TO \"" + username + "\"");
            stmt.executeUpdate("GRANT ALL ON SCHEMA public TO \"" + username + "\"");

            log.info("Schema 'public' in database '{}' now owned by '{}'", databaseName, username);
        } catch (SQLException e) {
            log.error("Failed to alter schema 'public' ownership", e);
            throw new RuntimeException("Failed to alter schema owner", e);
        }

        return new PostgreSQLCredentials(
                databaseName,
                username,
                password,
                adminDbInfo.host,
                adminDbInfo.port
        );
    }

    private String buildJdbcUrl(String host, String port, String database) {
        return String.format("jdbc:postgresql://%s:%s/%s", host, port, database);
    }

    private DatabaseConnectionInfo parseJdbcUrl(String jdbcUrl) {
        try {
            String cleanUrl = jdbcUrl.replace("jdbc:", "");
            URI uri = new URI(cleanUrl);
            String host = uri.getHost();
            int port = uri.getPort();
            String database = uri.getPath().replaceFirst("/", "");
            return new DatabaseConnectionInfo(host, String.valueOf(port), database);
        } catch (URISyntaxException e) {
            throw new RuntimeException("Invalid JDBC URL: " + jdbcUrl, e);
        }
    }

    private record DatabaseConnectionInfo(String host, String port, String database) {
    }
}
