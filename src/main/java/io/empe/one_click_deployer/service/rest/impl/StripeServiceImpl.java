package io.empe.one_click_deployer.service.rest.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stripe.Stripe;
import com.stripe.exception.StripeException;
import com.stripe.model.*;
import com.stripe.net.Webhook;
import io.empe.one_click_deployer.entity.Metadata;
import io.empe.one_click_deployer.entity.User;
import io.empe.one_click_deployer.entity.UserSubscription;
import io.empe.one_click_deployer.repository.UserRepository;
import io.empe.one_click_deployer.repository.UserSubscriptionRepository;
import io.empe.one_click_deployer.service.rest.StripeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;

@Service
@Slf4j
public class StripeServiceImpl implements StripeService {

    private final UserRepository userRepository;
    private final UserSubscriptionRepository subscriptionRepository;
    @Value("${stripe.api-key}")
    private String stripeApiKey;
    @Value("${stripe.webhook-secret}")
    private String webhookSecret;
    @Autowired
    private ObjectMapper objectMapper;

    public StripeServiceImpl(UserRepository userRepository, UserSubscriptionRepository subscriptionRepository) {
        this.userRepository = userRepository;
        this.subscriptionRepository = subscriptionRepository;
    }

    @Override
    public void handleWebhook(String payload, String sigHeader) throws Exception {
        Stripe.apiKey = stripeApiKey;

        Event event = Webhook.constructEvent(payload, sigHeader, webhookSecret);

        log.info("Received Stripe event: {}", event.getType());
        switch (event.getType()) {
            // TODO: Implement handling for subscription updates or deletions
            case "invoice.payment_succeeded" -> handleSubscriptionUpdated(event);
        }
    }

    private void handleSubscriptionUpdated(Event event) throws StripeException {
        EventDataObjectDeserializer deserializer = event.getDataObjectDeserializer();

        Invoice invoice = (Invoice) deserializer.deserializeUnsafe();

        String email = invoice.getCustomerEmail();
        String subscriptionId = invoice.getParent()
                .getSubscriptionDetails()
                .getSubscription();


        String stripeCustomer = invoice.getCustomer();

        if (email == null || subscriptionId == null) {
            log.warn("Missing email or subscription ID in session: {}", invoice.getId());
            return;
        }

        Subscription subscription = Subscription.retrieve(subscriptionId);
        String productId = subscription.getItems().getData().get(0).getPrice().getProduct();
        var periodEndSec = subscription.getItems().getData().get(0).getCurrentPeriodEnd();
        LocalDateTime periodEnd = Instant.ofEpochSecond(periodEndSec)
                .atZone(ZoneOffset.UTC)
                .toLocalDateTime();

        Product product = Product.retrieve(productId);
        if (product == null) {
            log.warn("No product found for subscription: {}", subscriptionId);
            return;
        }
        Map<String, String> rawMeta = product.getMetadata();

        userRepository.findByEmail(email).ifPresent(user -> {
            Optional<UserSubscription> oldSub = subscriptionRepository.findByUserAndSubscriptionActiveTrue(user);
            if (oldSub.isPresent()) {
                UserSubscription existingSub = oldSub.get();
                existingSub.setSubscriptionActive(false);
                subscriptionRepository.save(existingSub);
            }


            UserSubscription userSub = UserSubscription.builder().user(user).build();

            userSub.setStripeSubscriptionId(subscriptionId);
            userSub.setProductId(productId);
            userSub.setSubscriptionActive(true);
            userSub.setStripeCustomer(stripeCustomer);
            userSub.setCurrentPeriodEnd(periodEnd);

            if (userSub.getMetadata() == null) {
                userSub.setMetadata(new HashSet<>());
            }
            userSub.getMetadata().clear();

            rawMeta.forEach((key, value) -> {
                log.info("Adding metadata for key: {}, value: {}", key, value);
                Metadata meta = Metadata.builder()
                        .type(key)
                        .value(value)
                        .userSubscription(userSub)
                        .build();

                log.info("Created metadata: {}", meta);

                userSub.getMetadata().add(meta);
            });

            subscriptionRepository.save(userSub);
        });
    }

    @Override
    public String createCustomerPortalSession() throws StripeException {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User currentUser = (User) authentication.getPrincipal();

        Stripe.apiKey = stripeApiKey;

        var activeSubscription = subscriptionRepository.findByUserAndSubscriptionActiveTrue(currentUser);

        com.stripe.param.billingportal.SessionCreateParams params = com.stripe.param.billingportal.SessionCreateParams.builder()
                .setCustomer(activeSubscription.get().getStripeCustomer())
                .build();

        com.stripe.model.billingportal.Session portalSession = com.stripe.model.billingportal.Session.create(params);
        return portalSession.getUrl();
    }
}
