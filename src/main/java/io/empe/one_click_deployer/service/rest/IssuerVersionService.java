package io.empe.one_click_deployer.service.rest;

import io.empe.one_click_deployer.dto.CreateIssuerVersionRequest;
import io.empe.one_click_deployer.dto.IssuerVersionDto;
import io.empe.one_click_deployer.exception.external.IssuerVersionNotFoundException;

import java.util.List;

public interface IssuerVersionService {

    List<IssuerVersionDto> findAll();

    IssuerVersionDto findById(Long id) throws IssuerVersionNotFoundException;

    IssuerVersionDto create(CreateIssuerVersionRequest request);

    void delete(Long id) throws IssuerVersionNotFoundException;
}
