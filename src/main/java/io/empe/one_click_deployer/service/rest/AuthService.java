package io.empe.one_click_deployer.service.rest;

import io.empe.one_click_deployer.dto.LoginResponse;
import io.empe.one_click_deployer.dto.RegisterUserResponse;
import io.empe.one_click_deployer.exception.external.InvalidToken;
import io.empe.one_click_deployer.exception.external.MailSendError;

import java.util.UUID;

public interface AuthService {
    UUID requestEmailConfirmation(String email) throws MailSendError;

    LoginResponse confirmEmail(UUID uuid, String token) throws InvalidToken;

    void requestPasswordReset(String email) throws MailSendError;

    void resetPassword(UUID uuid, String token, String newPassword) throws InvalidToken;

    void resendEmailConfirmation(UUID uuid) throws MailSendError;
}