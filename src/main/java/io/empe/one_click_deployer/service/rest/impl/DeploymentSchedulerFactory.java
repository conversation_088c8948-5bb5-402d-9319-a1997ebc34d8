package io.empe.one_click_deployer.service.rest.impl;

import io.empe.one_click_deployer.entity.DeploymentType;
import io.empe.one_click_deployer.service.deployment.DeploymentScheduler;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@AllArgsConstructor
public class DeploymentSchedulerFactory {

    private final Map<String, DeploymentScheduler> schedulerMap;

    public DeploymentScheduler getScheduler(DeploymentType deploymentType) {
        String typeName = deploymentType.name();
        DeploymentScheduler scheduler = schedulerMap.get(typeName);
        if (scheduler == null) {
            throw new IllegalArgumentException("No scheduler found for deployment type: " + typeName);
        }
        return scheduler;
    }
}

