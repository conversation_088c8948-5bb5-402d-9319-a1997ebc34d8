package io.empe.one_click_deployer.service.rest;

import io.empe.one_click_deployer.dto.CreateIssuerDeploymentRequest;
import io.empe.one_click_deployer.dto.CreateIssuerDeploymentResponse;
import io.empe.one_click_deployer.dto.IssuerDeploymentDto;
import io.empe.one_click_deployer.dto.UpdateClientSecretResponse;
import io.empe.one_click_deployer.entity.User;
import io.empe.one_click_deployer.exception.external.*;

import java.util.List;
import java.util.UUID;

public interface IssuerDeploymentService {

    CreateIssuerDeploymentResponse createDeployment(CreateIssuerDeploymentRequest request, User user) throws NotEnoughDeploymentLeft, IssuerNameAlreadyExistsExceptionBase, VersionNotExistsExceptionBase, NotEnoughDeploymentLeft;

    List<IssuerDeploymentDto> getAllDeployments();

    List<IssuerDeploymentDto> getDeploymentsByUser(User user);

    IssuerDeploymentDto getDeploymentById(UUID id, User user) throws PermissionDeniedException, DeploymentNotFound;

    void deleteDeploymentById(UUID id, User user) throws PermissionDeniedException, DeploymentNotFound;

    UpdateClientSecretResponse updateClientSecret(UUID id, User user) throws PermissionDeniedException, DeploymentNotFound;

    IssuerDeploymentDto upgradeDeployment(UUID issuerId, Long versionId, User user) throws DeploymentNotFound, VersionNotExistsExceptionBase, InvalidVersionUpgradeException, PermissionDeniedException;

}
