package io.empe.one_click_deployer.service.rest.impl;

import com.mailersend.sdk.exceptions.MailerSendException;
import io.empe.one_click_deployer.dto.LoginResponse;
import io.empe.one_click_deployer.dto.RegisterUserResponse;
import io.empe.one_click_deployer.entity.Token;
import io.empe.one_click_deployer.entity.User;
import io.empe.one_click_deployer.exception.external.InvalidToken;
import io.empe.one_click_deployer.exception.external.MailSendError;
import io.empe.one_click_deployer.repository.TokenRepository;
import io.empe.one_click_deployer.repository.UserRepository;
import io.empe.one_click_deployer.service.rest.AuthService;
import io.empe.one_click_deployer.service.rest.MailSender;
import io.empe.one_click_deployer.service.rest.TokenService;
import io.empe.one_click_deployer.service.rest.UserService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.UUID;

@Service
public class AuthServiceImpl implements AuthService {

    private final UserRepository userRepository;
    private final TokenService tokenService;
    private final MailSender mailSender;
    private final PasswordEncoder passwordEncoder;
    private final UserService userService;
    private final TokenRepository tokenRepository;
    private final int tokenCooldownMinutes;

    public AuthServiceImpl(@Value("${application.token.cooldown:1}") int tokenCooldownMinutes, UserRepository userRepository, TokenService tokenService, MailSender mailSender, PasswordEncoder passwordEncoder, UserService userService, TokenRepository tokenRepository) {
        this.userRepository = userRepository;
        this.tokenService = tokenService;
        this.mailSender = mailSender;
        this.passwordEncoder = passwordEncoder;
        this.userService = userService;
        this.tokenRepository = tokenRepository;
        this.tokenCooldownMinutes = tokenCooldownMinutes;
    }

    @Transactional
    public UUID requestEmailConfirmation(String email) throws MailSendError {
        var userOpt = userRepository.findByEmail(email);
        if (userOpt.isEmpty()) {
            // we don't throw an error here, just silently ignore because it's a common security practice
            return null;
        }

        var user = userOpt.get();
        Token token = tokenService.createToken(user, Token.TokenType.EMAIL_CONFIRMATION);
        try {
            mailSender.sendEmailConfirmationEmail(user.getEmail(), token);
            return token.getId();
        } catch (MailerSendException e) {
            throw new MailSendError("Failed to send email confirmation");
        }
    }

    @Transactional
    public LoginResponse confirmEmail(UUID uuid, String token) throws InvalidToken {
        if (tokenService.validateToken(uuid, token)) {
            User user = tokenService.getUserByToken(token);

            if (!user.isEmailConfirmed()) {
                user.setEmailConfirmed(true);
                userRepository.save(user);
            }
            tokenService.invalidateToken(token);
            return this.userService.generateTokens(user);
        }
        throw new InvalidToken("Invalid or expired token for email confirmation");
    }

    @Transactional
    public void requestPasswordReset(String email) throws MailSendError {
        var userOpt = userRepository.findByEmail(email);
        if (userOpt.isEmpty()) {
            // we don't throw an error here, just silently ignore because it's a common security practice
            return;
        }


        var existingToken = tokenRepository.findByUserAndType(userOpt.get(), Token.TokenType.PASSWORD_RESET);
        if (existingToken.isPresent()) {
            if (existingToken.get().getCreationDate().isAfter(LocalDateTime.now().minusMinutes(tokenCooldownMinutes))) {
                // Token cooldown period is not over yet but we don't throw an error here, because it's a common security practice
                return;
            }

            tokenService.invalidateToken(existingToken.get().getToken());
        }

        var user = userOpt.get();
        Token token = tokenService.createToken(user, Token.TokenType.PASSWORD_RESET);

        try {
            mailSender.sendResetPasswordEmail(user.getEmail(), token);
        } catch (MailerSendException e) {
            throw new MailSendError("Failed to send password reset email");
        }
    }

    @Transactional
    public void resetPassword(UUID uuid, String token, String newPassword) throws InvalidToken {
        if (!tokenService.validateToken(uuid, token)) {
            throw new InvalidToken("Invalid or expired token for password reset");
        }

        User user = tokenService.getUserByToken(token);

        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);
        tokenService.invalidateToken(token);
    }

    @Transactional
    public void resendEmailConfirmation(UUID uuid) throws MailSendError {
        var existingToken = tokenRepository.findById(uuid);
        if (existingToken.isPresent()) {
            if (existingToken.get().getCreationDate().isAfter(LocalDateTime.now().minusMinutes(tokenCooldownMinutes))) {
                // Token cooldown period is not over yet but we don't throw an error here, because it's a common security practice
                return;
            }

            tokenService.invalidateToken(existingToken.get().getToken());
        }

        var user = existingToken.get().getUser();

        Token token = tokenService.createToken(user, Token.TokenType.EMAIL_CONFIRMATION);
        try {
            mailSender.sendEmailConfirmationEmail(user.getEmail(), token);
        } catch (MailerSendException e) {
            throw new MailSendError("Failed to send e-mail confirmation");
        }
    }
}
