package io.empe.one_click_deployer.service.rest.impl;

import io.empe.one_click_deployer.repository.RefreshTokenRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
@AllArgsConstructor
public class RefreshTokenService {

    private final RefreshTokenRepository refreshTokenRepository;

    //    @Scheduled(cron = "0 0 0 * * ?")
    public void removeExpiredTokens() {
        LocalDateTime now = LocalDateTime.now();
        refreshTokenRepository.deleteAllByExpiryDateBefore(now);
    }
}

