package io.empe.one_click_deployer.service.rest.impl;

import io.empe.one_click_deployer.dto.*;
import io.empe.one_click_deployer.entity.*;
import io.empe.one_click_deployer.event.IssuerDeploymentCreatedEvent;
import io.empe.one_click_deployer.event.IssuerDeploymentDeletedEvent;
import io.empe.one_click_deployer.event.IssuerDeploymentSecretUpgradeEvent;
import io.empe.one_click_deployer.event.IssuerDeploymentUpgradeEvent;
import io.empe.one_click_deployer.exception.external.*;
import io.empe.one_click_deployer.mapper.IssuerDeploymentMapper;
import io.empe.one_click_deployer.repository.*;
import io.empe.one_click_deployer.service.rest.IssuerDeploymentService;
import io.empe.one_click_deployer.utils.AuthKeyGenerator;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static io.empe.one_click_deployer.utils.VersionUtil.isUpgradeValid;
import static io.empe.one_click_deployer.utils.VersionUtil.parseVersion;

@Service
@RequiredArgsConstructor
public class IssuerDeploymentServiceImpl implements IssuerDeploymentService {

    private static final String HTTPS_PROTOCOL = "https://";
    private static final String VERSION_NOT_EXISTS_MSG = "Specified version or 'latest' version does not exist.";
    private static final String NOT_ENOUGH_DEPLOYMENT_LEFT_MSG = "Not enough deployments left for this user for this blockchain type.";
    private static final String ISSUER_NAME_EXISTS_MSG = "Deployment with this issuer name already exists.";
    private static final String PERMISSION_DENIED_MSG = "You do not have permission to view this deployment.";
    private static final String INVALID_VERSION_ERROR_MSG = "Cannot downgrade from version %s to %s";
    private static final String DEPLOYMENT_NOT_FOUND_MSG = "Deployment not found";
    private static final String VERSION_TO_UPGRADE_NOT_EXISTS_MSG = "Version to upgrade does not exist.";
    private static final String PERMISSION_DENIED_UPDATE_MSG = "You do not have permission to update this deployment.";
    private static final String LATEST_VERSION = "latest";

    private final IssuerDeploymentRepository issuerDeploymentRepository;
    private final UserRepository userRepository;
    private final IssuerDeploymentMapper issuerDeploymentMapper = IssuerDeploymentMapper.INSTANCE;
    private final ApplicationEventPublisher eventPublisher;
    private final IssuerVersionRepository issuerVersionRepository;
    private final BlockchainConfigurationRepository blockchainConfigurationRepository;
    private final UserSubscriptionRepository userSubscriptionRepository;

    @Value("${one-click.issuerSuffix}")
    private String issuerSuffix;

    @Value("${one-click.domain}")
    private String domain;

    @Transactional
    @Override
    public CreateIssuerDeploymentResponse createDeployment(CreateIssuerDeploymentRequest request, User user) throws IssuerNameAlreadyExistsExceptionBase, VersionNotExistsExceptionBase, NotEnoughDeploymentLeft {
        String issuerNameWithSuffix = request.getIssuerName() + issuerSuffix;
        checkIssuerNameExists(issuerNameWithSuffix);

        BlockchainConfiguration blockchainConfiguration = blockchainConfigurationRepository.findById(request.getBlockchainConfigurationId())
                .orElseThrow(() -> new IllegalArgumentException("Blockchain configuration not found"));

        validateUserSubscription(user, blockchainConfiguration);
        IssuerDeployment issuerDeployment = buildIssuerDeployment(request, user, issuerNameWithSuffix);
        issuerDeployment.setBlockchainConfiguration(blockchainConfiguration);
        IssuerDeployment savedDeployment = saveIssuerDeployment(issuerDeployment, user);

        String authKey = AuthKeyGenerator.generateAuthKey();
        eventPublisher.publishEvent(new IssuerDeploymentCreatedEvent(this, savedDeployment, authKey));

        return new CreateIssuerDeploymentResponse(issuerDeploymentMapper.toDto(savedDeployment), authKey);
    }


    private void validateUserSubscription(User user, BlockchainConfiguration blockchainConfiguration) throws NotEnoughDeploymentLeft {
        UserSubscription sub = userSubscriptionRepository
                .findByUserAndSubscriptionActiveTrue(user)
                .orElseThrow(() -> new NotEnoughDeploymentLeft(NOT_ENOUGH_DEPLOYMENT_LEFT_MSG));

        OneClickDeploymentMetadataType requiredKey = switch (blockchainConfiguration.getType()) {
            case BlockchainType.MAINNET -> OneClickDeploymentMetadataType.ISSUER_MAINNET_DEPLOYMENT_LIMIT;
            case BlockchainType.TESTNET -> OneClickDeploymentMetadataType.ISSUER_TESTNET_DEPLOYMENT_LIMIT;
        };

        Metadata quotaRow = sub.getMetadata().stream()
                .filter(m -> {
                    try {
                        return OneClickDeploymentMetadataType.valueOf(m.getType()) == requiredKey;
                    } catch (IllegalArgumentException e) {
                        return false;
                    }
                })
                .findFirst()
                .orElseThrow(() -> new NotEnoughDeploymentLeft(NOT_ENOUGH_DEPLOYMENT_LEFT_MSG));

        Integer allUserIssuerDeploymentsCount = issuerDeploymentRepository.countByUserIdAndBlockchainConfiguration_Type(user.getId(), blockchainConfiguration.getType());

        int quota = Integer.parseInt(quotaRow.getValue());
        if (allUserIssuerDeploymentsCount + 1 > quota) {
            throw new NotEnoughDeploymentLeft(NOT_ENOUGH_DEPLOYMENT_LEFT_MSG);
        }
    }

    private void checkIssuerNameExists(String issuerNameWithSuffix) throws IssuerNameAlreadyExistsExceptionBase {
        if (issuerDeploymentRepository.existsByIssuerName(issuerNameWithSuffix)) {
            throw new IssuerNameAlreadyExistsExceptionBase(ISSUER_NAME_EXISTS_MSG);
        }
    }

    private IssuerDeployment buildIssuerDeployment(CreateIssuerDeploymentRequest request, User user, String issuerNameWithSuffix) throws VersionNotExistsExceptionBase {
        IssuerDeployment issuerDeployment = new IssuerDeployment();
        issuerDeployment.setIssuerName(issuerNameWithSuffix);
        issuerDeployment.setFullHost(HTTPS_PROTOCOL + issuerNameWithSuffix + "." + domain);
        issuerDeployment.setStatus(Status.INIT);
        issuerDeployment.setDeploymentType(request.getDeploymentType());
        issuerDeployment.setUser(user);
        issuerDeployment.setIssuerReadableName(request.getIssuerReadableName());
        issuerDeployment.setOrganization(request.getOrganizationName());

        setIssuerVersion(request, issuerDeployment);

        return issuerDeployment;
    }

    private void setIssuerVersion(CreateIssuerDeploymentRequest request, IssuerDeployment issuerDeployment) throws VersionNotExistsExceptionBase {
        Optional<IssuerVersion> issuerVersion = Optional.empty();
        if (request.getVersionId() != null) {
            issuerVersion = issuerVersionRepository.findById(request.getVersionId());
        }
        if (issuerVersion.isEmpty()) {
            issuerVersion = issuerVersionRepository.findByLabel(LATEST_VERSION);
        }
        if (issuerVersion.isPresent()) {
            issuerDeployment.setIssuerVersion(issuerVersion.get());
        } else {
            throw new VersionNotExistsExceptionBase(VERSION_NOT_EXISTS_MSG);
        }
    }

    private IssuerDeployment saveIssuerDeployment(IssuerDeployment issuerDeployment, User user) {
        IssuerDeployment savedDeployment = issuerDeploymentRepository.save(issuerDeployment);
        userRepository.save(user);
        return savedDeployment;
    }

    @Override
    public List<IssuerDeploymentDto> getAllDeployments() {
        return issuerDeploymentRepository.findAll().stream()
                .map(issuerDeploymentMapper::toDto)
                .toList();
    }

    @Override
    public List<IssuerDeploymentDto> getDeploymentsByUser(User user) {
        return issuerDeploymentRepository.findByUserId(user.getId()).stream()
                .map(issuerDeploymentMapper::toDto)
                .toList();
    }

    @Override
    public IssuerDeploymentDto getDeploymentById(UUID id, User user) throws PermissionDeniedException, DeploymentNotFound {
        IssuerDeployment deployment = findDeploymentById(id);
        validatePermissionToViewDeployment(deployment, user);
        return issuerDeploymentMapper.toDto(deployment);
    }

    private IssuerDeployment findDeploymentById(UUID id) throws DeploymentNotFound {
        return issuerDeploymentRepository.findById(id)
                .orElseThrow(() -> new DeploymentNotFound(DEPLOYMENT_NOT_FOUND_MSG));
    }

    private void validatePermissionToViewDeployment(IssuerDeployment deployment, User user) throws PermissionDeniedException {
        if (!deployment.getUser().getId().equals(user.getId()) && !user.getRole().equals(User.Role.ADMIN)) {
            throw new PermissionDeniedException(PERMISSION_DENIED_MSG);
        }
    }

    @Override
    @Transactional
    public void deleteDeploymentById(UUID id, User user) throws PermissionDeniedException, DeploymentNotFound {
        IssuerDeployment deployment = findDeploymentById(id);
        validatePermissionToDeleteDeployment(deployment, user);

        deployment.setStatus(Status.DELETED_SCHEDULED);
        issuerDeploymentRepository.save(deployment);
        eventPublisher.publishEvent(new IssuerDeploymentDeletedEvent(this, deployment));
    }

    private void validatePermissionToDeleteDeployment(IssuerDeployment deployment, User user) throws PermissionDeniedException {
        if (!deployment.getUser().getId().equals(user.getId()) && !user.getRole().equals(User.Role.ADMIN)) {
            throw new PermissionDeniedException(PERMISSION_DENIED_MSG);
        }
    }

    @Override
    @Transactional
    public UpdateClientSecretResponse updateClientSecret(UUID id, User user) throws PermissionDeniedException, DeploymentNotFound {
        IssuerDeployment deployment = findDeploymentById(id);
        validatePermissionToUpdateDeployment(deployment, user);

        String authKey = AuthKeyGenerator.generateAuthKey();
        eventPublisher.publishEvent(new IssuerDeploymentSecretUpgradeEvent(this, deployment, authKey));

        return new UpdateClientSecretResponse(authKey);
    }

    private void validatePermissionToUpdateDeployment(IssuerDeployment deployment, User user) throws PermissionDeniedException {
        if (!deployment.getUser().getId().equals(user.getId()) && !user.getRole().equals(User.Role.ADMIN)) {
            throw new PermissionDeniedException(PERMISSION_DENIED_UPDATE_MSG);
        }
    }

    @Override
    @Transactional
    public IssuerDeploymentDto upgradeDeployment(UUID issuerId, Long versionId, User user) throws DeploymentNotFound, VersionNotExistsExceptionBase, InvalidVersionUpgradeException, PermissionDeniedException {
        IssuerDeployment currentDeployment = findDeploymentById(issuerId);
        validatePermissionToViewDeployment(currentDeployment, user);

        String currentVersion = currentDeployment.getIssuerVersion().getVersion();
        IssuerVersion newVersion = findVersionToUpgrade(versionId);

        validateVersionUpgrade(currentVersion, newVersion.getVersion());

        currentDeployment.setIssuerVersion(newVersion);
        issuerDeploymentRepository.save(currentDeployment);

        eventPublisher.publishEvent(new IssuerDeploymentUpgradeEvent(this, currentDeployment));

        return issuerDeploymentMapper.toDto(currentDeployment);
    }

    private IssuerVersion findVersionToUpgrade(Long versionId) throws VersionNotExistsExceptionBase {
        return issuerVersionRepository.findById(versionId)
                .orElseThrow(() -> new VersionNotExistsExceptionBase(VERSION_TO_UPGRADE_NOT_EXISTS_MSG));
    }

    private void validateVersionUpgrade(String currentVersion, String newVersion) throws InvalidVersionUpgradeException {
        if (!isUpgradeValid(parseVersion(currentVersion), newVersion)) {
            throw new InvalidVersionUpgradeException(String.format(INVALID_VERSION_ERROR_MSG, currentVersion, newVersion));
        }
    }
}
