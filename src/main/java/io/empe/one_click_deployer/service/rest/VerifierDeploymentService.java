package io.empe.one_click_deployer.service.rest;

import io.empe.one_click_deployer.dto.CreateVerifierDeploymentRequest;
import io.empe.one_click_deployer.dto.CreateVerifierDeploymentResponse;
import io.empe.one_click_deployer.dto.UpdateClientSecretResponse;
import io.empe.one_click_deployer.dto.VerifierDeploymentDto;
import io.empe.one_click_deployer.entity.User;
import io.empe.one_click_deployer.exception.external.*;

import java.util.List;
import java.util.UUID;

public interface VerifierDeploymentService {

    CreateVerifierDeploymentResponse createDeployment(CreateVerifierDeploymentRequest request, User user) throws NotEnoughDeploymentLeft, VerifierNameAlreadyExistsExceptionBase, VersionNotExistsExceptionBase;

    List<VerifierDeploymentDto> getAllDeployments();

    List<VerifierDeploymentDto> getDeploymentsByUser(User user);

    VerifierDeploymentDto getDeploymentById(UUID id, User user) throws PermissionDeniedException, DeploymentNotFound;

    void deleteDeploymentById(UUID id, User user) throws PermissionDeniedException, DeploymentNotFound;

    UpdateClientSecretResponse updateClientSecret(UUID id, User user) throws PermissionDeniedException, DeploymentNotFound;

    VerifierDeploymentDto upgradeDeployment(UUID verifierId, Long versionId, User user) throws DeploymentNotFound, VersionNotExistsExceptionBase, InvalidVersionUpgradeException, PermissionDeniedException;
}
