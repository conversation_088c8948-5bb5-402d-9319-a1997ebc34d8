package io.empe.one_click_deployer.service.rest.impl;

import io.empe.one_click_deployer.dto.CreateIssuerVersionRequest;
import io.empe.one_click_deployer.dto.IssuerVersionDto;
import io.empe.one_click_deployer.entity.IssuerVersion;
import io.empe.one_click_deployer.exception.external.IssuerVersionNotFoundException;
import io.empe.one_click_deployer.mapper.IssuerVersionMapper;
import io.empe.one_click_deployer.repository.IssuerVersionRepository;
import io.empe.one_click_deployer.service.rest.IssuerVersionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional
public class IssuerVersionServiceImpl implements IssuerVersionService {

    private final IssuerVersionRepository repository;
    private final IssuerVersionMapper mapper;

    public IssuerVersionServiceImpl(IssuerVersionRepository repository, IssuerVersionMapper mapper) {
        this.repository = repository;
        this.mapper = mapper;
    }

    @Transactional
    public IssuerVersionDto create(CreateIssuerVersionRequest createIssuerVersionRequest) {
        if ("latest".equals(createIssuerVersionRequest.getLabel())) {
            repository.findByLabel("latest").ifPresent(existing -> {
                existing.setLabel("latest-old-" + System.currentTimeMillis());
                repository.save(existing);
                repository.flush();
            });
        }

        IssuerVersion issuerVersion = mapper.toEntity(createIssuerVersionRequest);
        IssuerVersion savedIssuerVersion = repository.save(issuerVersion);
        return mapper.toDto(savedIssuerVersion);
    }

    @Override
    public IssuerVersionDto findById(Long id) throws IssuerVersionNotFoundException {
        IssuerVersion entity = repository.findById(id)
                .orElseThrow(() -> new IssuerVersionNotFoundException("IssuerVersion with ID " + id + " not found"));
        return mapper.toDto(entity);
    }

    @Override
    public List<IssuerVersionDto> findAll() {
        return repository.findAll().stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    public void delete(Long id) throws IssuerVersionNotFoundException {
        if (!repository.existsById(id)) {
            throw new IssuerVersionNotFoundException("IssuerVersion with ID " + id + " not found");
        }
        repository.deleteById(id);
    }
}
