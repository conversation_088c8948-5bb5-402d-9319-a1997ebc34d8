package io.empe.one_click_deployer.service.rest;

import io.empe.one_click_deployer.dto.CreateUserRequest;
import io.empe.one_click_deployer.dto.LoginResponse;
import io.empe.one_click_deployer.dto.UserDto;
import io.empe.one_click_deployer.entity.User;
import io.empe.one_click_deployer.exception.external.CreateUserInvalidParams;
import io.empe.one_click_deployer.exception.external.RefreshTokenNotExistOrExpiredException;
import io.empe.one_click_deployer.exception.external.UserAlreadyExists;
import io.empe.one_click_deployer.exception.external.UserEmailNotConfirmed;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;

public interface UserService extends UserDetailsService {
    void registerUser(CreateUserRequest createUserRequest) throws CreateUserInvalidParams, UserAlreadyExists;

    UserDetails loadUserByEmail(String email);

    LoginResponse authenticateUser(String email, String password) throws UserEmailNotConfirmed;

    UserDto getCurrentUser();

    LoginResponse refreshAccessToken(String refreshToken) throws RefreshTokenNotExistOrExpiredException;

    LoginResponse generateTokens(User user);
}

