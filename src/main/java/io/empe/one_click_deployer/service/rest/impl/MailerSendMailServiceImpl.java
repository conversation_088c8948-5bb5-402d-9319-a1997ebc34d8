package io.empe.one_click_deployer.service.rest.impl;

import com.mailersend.sdk.MailerSend;
import com.mailersend.sdk.emails.Email;
import com.mailersend.sdk.exceptions.MailerSendException;
import io.empe.one_click_deployer.entity.Token;
import io.empe.one_click_deployer.service.rest.MailSender;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
public class MailerSendMailServiceImpl implements MailSender {

    private static final Logger log = LoggerFactory.getLogger(MailerSendMailServiceImpl.class);
    private final MailerSend mailerSend;
    private final String fromEmail;
    private final String fromName;
    private final String emailConfirmationTemplateId;
    private final String resetPasswordTemplateId;
    private final String resetPasswordUrl;
    private final String emailConfirmationUrl;

    public MailerSendMailServiceImpl(
            @Value("${mailersend.api.token}") String mailersendApiToken,
            @Value("${mailersend.from.email}") String fromEmail,
            @Value("${mailersend.from.name:One Click Deployer}") String fromName,
            @Value("${mailersend.template.email-confirmation-id}") String emailConfirmationTemplateId,
            @Value("${mailersend.template.reset-password-id}") String resetPasswordTemplateId,
            @Value("${mailersend.template.reset-password-url}") String resetPasswordUrl,
            @Value("${mailersend.template.email-confirmation-url}") String emailConfirmationUrl
    ) {
        this.mailerSend = new MailerSend();
        this.mailerSend.setToken(mailersendApiToken);
        this.fromEmail = fromEmail;
        this.fromName = fromName;
        this.emailConfirmationTemplateId = emailConfirmationTemplateId;
        this.resetPasswordTemplateId = resetPasswordTemplateId;
        this.resetPasswordUrl = resetPasswordUrl;
        this.emailConfirmationUrl = emailConfirmationUrl;
    }

    @Override
    public void sendEmailConfirmationEmail(String toEmailAddress, Token token) throws MailerSendException {
        Email email = new Email();
        email.setFrom(fromName, fromEmail);
        email.addRecipient(toEmailAddress, toEmailAddress);
        email.setSubject("Email Confirmation");
        email.setTemplateId(emailConfirmationTemplateId);
        email.addPersonalization("url", emailConfirmationUrl + "?token=" + token.getToken() + "&uuid=" + token.getId());
        email.addPersonalization("token", token.getToken());

        send(email);
    }

    @Override
    public void sendResetPasswordEmail(String toEmailAddress, Token token) throws MailerSendException {
        Email email = new Email();
        email.setFrom(fromName, fromEmail);
        email.addRecipient(toEmailAddress, toEmailAddress);
        email.setSubject("Reset Password");
        email.setTemplateId(resetPasswordTemplateId);
        email.addPersonalization("url", resetPasswordUrl + "?token=" + token.getToken() + "&uuid=" + token.getId());

        send(email);
    }

    private void send(Email email) throws MailerSendException {
        mailerSend.emails().send(email);
    }
}