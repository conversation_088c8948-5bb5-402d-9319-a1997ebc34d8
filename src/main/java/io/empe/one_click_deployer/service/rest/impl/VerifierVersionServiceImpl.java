package io.empe.one_click_deployer.service.rest.impl;

import io.empe.one_click_deployer.dto.CreateVerifierVersionRequest;
import io.empe.one_click_deployer.dto.VerifierVersionDto;
import io.empe.one_click_deployer.entity.VerifierVersion;
import io.empe.one_click_deployer.exception.external.VerifierVersionNotFoundException;
import io.empe.one_click_deployer.mapper.VerifierVersionMapper;
import io.empe.one_click_deployer.repository.VerifierVersionRepository;
import io.empe.one_click_deployer.service.rest.VerifierVersionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional
public class VerifierVersionServiceImpl implements VerifierVersionService {

    private final VerifierVersionRepository repository;
    private final VerifierVersionMapper mapper;

    public VerifierVersionServiceImpl(VerifierVersionRepository repository, VerifierVersionMapper mapper) {
        this.repository = repository;
        this.mapper = mapper;
    }

    @Transactional
    public VerifierVersionDto create(CreateVerifierVersionRequest createVerifierVersionRequest) {
        if ("latest".equals(createVerifierVersionRequest.getLabel())) {
            repository.findByLabel("latest").ifPresent(existing -> {
                existing.setLabel("latest-old-" + System.currentTimeMillis());
                repository.save(existing);
                repository.flush();
            });
        }

        VerifierVersion verifierVersion = mapper.toEntity(createVerifierVersionRequest);
        VerifierVersion savedVerierVersion = repository.save(verifierVersion);
        return mapper.toDto(savedVerierVersion);
    }

    @Override
    public VerifierVersionDto findById(Long id) throws VerifierVersionNotFoundException {
        VerifierVersion entity = repository.findById(id)
                .orElseThrow(() -> new VerifierVersionNotFoundException("IssuerVersion with ID " + id + " not found"));
        return mapper.toDto(entity);
    }

    @Override
    public List<VerifierVersionDto> findAll() {
        return repository.findAll().stream()
                .map(mapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    public void delete(Long id) throws VerifierVersionNotFoundException {
        if (!repository.existsById(id)) {
            throw new VerifierVersionNotFoundException("IssuerVersion with ID " + id + " not found");
        }
        repository.deleteById(id);
    }
}
