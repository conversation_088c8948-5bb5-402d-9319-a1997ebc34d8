package io.empe.one_click_deployer.service.rest.impl;

import io.empe.one_click_deployer.config.JwtService;
import io.empe.one_click_deployer.dto.CreateUserRequest;
import io.empe.one_click_deployer.dto.LoginResponse;
import io.empe.one_click_deployer.dto.UserDto;
import io.empe.one_click_deployer.entity.RefreshToken;
import io.empe.one_click_deployer.entity.User;
import io.empe.one_click_deployer.exception.external.RefreshTokenNotExistOrExpiredException;
import io.empe.one_click_deployer.exception.external.UserAlreadyExists;
import io.empe.one_click_deployer.exception.external.UserEmailNotConfirmed;
import io.empe.one_click_deployer.mapper.SubscriptionMapper;
import io.empe.one_click_deployer.mapper.UserMapper;
import io.empe.one_click_deployer.repository.RefreshTokenRepository;
import io.empe.one_click_deployer.repository.UserRepository;
import io.empe.one_click_deployer.repository.UserSubscriptionRepository;
import io.empe.one_click_deployer.service.rest.UserService;
import lombok.AllArgsConstructor;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

@Service
@AllArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserRepository userRepository;
    private final UserSubscriptionRepository userSubscriptionRepository;
    private final RefreshTokenRepository refreshTokenRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtService jwtService;

    private final UserMapper userMapper = UserMapper.INSTANCE;

    @Transactional
    public void registerUser(CreateUserRequest req) throws UserAlreadyExists {
        if (userRepository.findByEmail(req.getEmail()).isPresent()) {
            throw new UserAlreadyExists("User already exists");
        }

        User newUser = User.builder()
                .email(req.getEmail())
                .password(passwordEncoder.encode(req.getPassword()))
                .firstName(req.getFirstName())
                .lastName(req.getLastName())
                .organizationName(req.getOrganizationName())
                .role(User.Role.USER)
                .phoneNumber(req.getPhoneNumber())
                .build();

        userRepository.save(newUser);
    }

    @Override
    public LoginResponse authenticateUser(String email, String rawPassword) throws UserEmailNotConfirmed {
        User user = (User) loadUserByEmail(email);

        if (!user.isEmailConfirmed()) {
            throw new UserEmailNotConfirmed("User email is not confirmed");
        }

        return generateTokens(user);
    }


    /**
     * Creates an access token, a refresh token, stores the refresh token,
     * and returns them wrapped in {@link LoginResponse}.
     */
    public LoginResponse generateTokens(User user) {
        String jwt = jwtService.generateToken(user.getEmail());
        String refresh = UUID.randomUUID().toString();

        RefreshToken rt = RefreshToken.builder()
                .token(refresh)
                .expiryDate(LocalDateTime.now().plusDays(7))
                .user(user)
                .build();

        refreshTokenRepository.save(rt);

        return new LoginResponse(jwt, refresh);
    }

    @Override
    public UserDto getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User currentUser = (User) authentication.getPrincipal();
        var subscription = userSubscriptionRepository.findByUserAndSubscriptionActiveTrue(currentUser);
        var userDto = userMapper.toUserDto(currentUser);
        if (!subscription.isEmpty()) {
            userDto.setSubscriptionActive(subscription.get().isSubscriptionActive());
            userDto.setMetadata(SubscriptionMapper.INSTANCE.toMetadataDtoList(subscription.get().getMetadata()));
        } else {
            userDto.setSubscriptionActive(false);
        }

        return userDto;
    }

    @Override
    public LoginResponse refreshAccessToken(String refreshToken) throws RefreshTokenNotExistOrExpiredException {
        RefreshToken tokenEntity = refreshTokenRepository.findByToken(refreshToken)
                .orElseThrow(() -> new RefreshTokenNotExistOrExpiredException("Refresh token does not exist or has expired"));

        if (tokenEntity.getExpiryDate().isBefore(LocalDateTime.now())) {
            throw new RefreshTokenNotExistOrExpiredException("Refresh token has expired");
        }

        tokenEntity.setExpiryDate(LocalDateTime.now().plusDays(7));
        refreshTokenRepository.save(tokenEntity);

        String newAccessToken = jwtService.generateToken(tokenEntity.getUser().getUsername());

        return new LoginResponse(newAccessToken, refreshToken);
    }


    @Override
    public UserDetails loadUserByEmail(String email) {
        Optional<User> userDetail = userRepository.findByEmail(email);
        return userDetail.orElseThrow(() -> new UsernameNotFoundException("User not found: " + email));
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        Optional<User> userDetail = userRepository.findByEmail(username);
        return userDetail.orElseThrow(() -> new UsernameNotFoundException("User not found: " + username));
    }
}
