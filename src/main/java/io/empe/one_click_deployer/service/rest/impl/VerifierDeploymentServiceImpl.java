package io.empe.one_click_deployer.service.rest.impl;

import io.empe.one_click_deployer.dto.*;
import io.empe.one_click_deployer.entity.*;
import io.empe.one_click_deployer.event.VerifierDeploymentCreatedEvent;
import io.empe.one_click_deployer.event.VerifierDeploymentDeletedEvent;
import io.empe.one_click_deployer.event.VerifierDeploymentSecretUpgradeEvent;
import io.empe.one_click_deployer.event.VerifierDeploymentUpgradeEvent;
import io.empe.one_click_deployer.exception.external.*;
import io.empe.one_click_deployer.mapper.VerifierDeploymentMapper;
import io.empe.one_click_deployer.repository.*;
import io.empe.one_click_deployer.service.rest.VerifierDeploymentService;
import io.empe.one_click_deployer.utils.AuthKeyGenerator;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static io.empe.one_click_deployer.utils.VersionUtil.isUpgradeValid;
import static io.empe.one_click_deployer.utils.VersionUtil.parseVersion;

@Service
@RequiredArgsConstructor
public class VerifierDeploymentServiceImpl implements VerifierDeploymentService {

    private static final String HTTPS_PROTOCOL = "https://";
    private static final String VERSION_NOT_EXISTS_MSG = "Specified version or 'latest' version does not exist.";
    private static final String NOT_ENOUGH_DEPLOYMENT_LEFT_MSG = "Not enough deployments left for this user for this blockchain type.";
    private static final String VERIFIER_NAME_EXISTS_MSG = "Deployment with this verifier name already exists.";
    private static final String PERMISSION_DENIED_MSG = "You do not have permission to view this deployment.";
    private static final String INVALID_VERSION_ERROR_MSG = "Cannot downgrade from version %s to %s";
    private static final String DEPLOYMENT_NOT_FOUND_MSG = "Deployment not found";
    private static final String VERSION_TO_UPGRADE_NOT_EXISTS_MSG = "Version to upgrade does not exist.";
    private static final String PERMISSION_DENIED_UPDATE_MSG = "You do not have permission to update this deployment.";
    private static final String LATEST_VERSION_MSG = "latest";

    private final VerifierDeploymentRepository verifierDeploymentRepository;
    private final UserRepository userRepository;
    private final VerifierDeploymentMapper verifierDeploymentMapper = VerifierDeploymentMapper.INSTANCE;
    private final ApplicationEventPublisher eventPublisher;
    private final VerifierVersionRepository verifierVersionRepository;
    private final BlockchainConfigurationRepository blockchainConfigurationRepository;
    private final UserSubscriptionRepository userSubscriptionRepository;

    @Value("${one-click.verifierSuffix}")
    private String verifierSuffix;

    @Value("${one-click.domain}")
    private String domain;

    @Transactional
    @Override
    public CreateVerifierDeploymentResponse createDeployment(CreateVerifierDeploymentRequest request, User user) throws NotEnoughDeploymentLeft, VerifierNameAlreadyExistsExceptionBase, VersionNotExistsExceptionBase {
        String verifierNameWithSuffix = request.getVerifierName() + verifierSuffix;
        checkVerifierNameExists(verifierNameWithSuffix);

        BlockchainConfiguration blockchainConfiguration = blockchainConfigurationRepository.findById(request.getBlockchainConfigurationId())
                .orElseThrow(() -> new IllegalArgumentException("Blockchain configuration not found"));

        validateUserSubscription(user, blockchainConfiguration);

        VerifierDeployment verifierDeployment = buildVerifierDeployment(request, user, verifierNameWithSuffix);
        verifierDeployment.setBlockchainConfiguration(blockchainConfiguration);
        VerifierDeployment savedDeployment = saveVerifierDeployment(verifierDeployment, user);

        String authKey = AuthKeyGenerator.generateAuthKey();
        eventPublisher.publishEvent(new VerifierDeploymentCreatedEvent(this, savedDeployment, authKey));

        return new CreateVerifierDeploymentResponse(verifierDeploymentMapper.toDto(savedDeployment), authKey);
    }

    private void validateUserSubscription(User user, BlockchainConfiguration blockchainConfiguration) throws NotEnoughDeploymentLeft {
        UserSubscription sub = userSubscriptionRepository
                .findByUserAndSubscriptionActiveTrue(user)
                .orElseThrow(() -> new NotEnoughDeploymentLeft(NOT_ENOUGH_DEPLOYMENT_LEFT_MSG));

        OneClickDeploymentMetadataType requiredKey = switch (blockchainConfiguration.getType()) {
            case BlockchainType.MAINNET -> OneClickDeploymentMetadataType.VERIFIER_MAINNET_DEPLOYMENT_LIMIT;
            case BlockchainType.TESTNET -> OneClickDeploymentMetadataType.VERIFIER_TESTNET_DEPLOYMENT_LIMIT;
        };

        Metadata quotaRow = sub.getMetadata().stream()
                .filter(m -> {
                    try {
                        return OneClickDeploymentMetadataType.valueOf(m.getType()) == requiredKey;
                    } catch (IllegalArgumentException e) {
                        return false;
                    }
                })
                .findFirst()
                .orElseThrow(() -> new NotEnoughDeploymentLeft(NOT_ENOUGH_DEPLOYMENT_LEFT_MSG));

        Integer allUserVerifierDeploymentsCount = verifierDeploymentRepository.countByUserIdAndBlockchainConfiguration_Type(user.getId(), blockchainConfiguration.getType());

        int quota = Integer.parseInt(quotaRow.getValue());
        if (allUserVerifierDeploymentsCount + 1 > quota) {
            throw new NotEnoughDeploymentLeft(NOT_ENOUGH_DEPLOYMENT_LEFT_MSG);
        }
    }

    private void checkVerifierNameExists(String verifierNameWithSuffix) throws VerifierNameAlreadyExistsExceptionBase {
        if (verifierDeploymentRepository.existsByVerifierName(verifierNameWithSuffix)) {
            throw new VerifierNameAlreadyExistsExceptionBase(VERIFIER_NAME_EXISTS_MSG);
        }
    }

    private VerifierDeployment buildVerifierDeployment(CreateVerifierDeploymentRequest request, User user, String verifierNameWithSuffix) throws VersionNotExistsExceptionBase {
        VerifierDeployment verifierDeployment = new VerifierDeployment();
        verifierDeployment.setVerifierName(verifierNameWithSuffix);
        verifierDeployment.setFullHost(HTTPS_PROTOCOL + verifierNameWithSuffix + "." + domain);
        verifierDeployment.setStatus(Status.INIT);
        verifierDeployment.setVerifierReadableName(request.getVerifierReadableName());
        verifierDeployment.setDeploymentType(request.getDeploymentType());
        verifierDeployment.setUser(user);

        setVerifierVersion(request, verifierDeployment);

        return verifierDeployment;
    }

    private void setVerifierVersion(CreateVerifierDeploymentRequest request, VerifierDeployment verifierDeployment) throws VersionNotExistsExceptionBase {
        Optional<VerifierVersion> verifierVersion = Optional.empty();
        if (request.getVersionId() != null) {
            verifierVersion = verifierVersionRepository.findById(request.getVersionId());
        }
        if (verifierVersion.isEmpty()) {
            verifierVersion = verifierVersionRepository.findByLabel(LATEST_VERSION_MSG);
        }
        if (verifierVersion.isPresent()) {
            verifierDeployment.setVerifierVersion(verifierVersion.get());
        } else {
            throw new VersionNotExistsExceptionBase(VERSION_NOT_EXISTS_MSG);
        }
    }

    private VerifierDeployment saveVerifierDeployment(VerifierDeployment verifierDeployment, User user) {
        VerifierDeployment savedDeployment = verifierDeploymentRepository.save(verifierDeployment);
        userRepository.save(user);
        return savedDeployment;
    }

    @Override
    public List<VerifierDeploymentDto> getAllDeployments() {
        return verifierDeploymentRepository.findAll().stream()
                .map(verifierDeploymentMapper::toDto)
                .toList();
    }

    @Override
    public List<VerifierDeploymentDto> getDeploymentsByUser(User user) {
        return verifierDeploymentRepository.findByUserId(user.getId()).stream()
                .map(verifierDeploymentMapper::toDto)
                .toList();
    }

    @Override
    public VerifierDeploymentDto getDeploymentById(UUID id, User user) throws PermissionDeniedException, DeploymentNotFound {
        VerifierDeployment deployment = findDeploymentById(id);
        validatePermissionToViewDeployment(deployment, user);
        return verifierDeploymentMapper.toDto(deployment);
    }

    private VerifierDeployment findDeploymentById(UUID id) throws DeploymentNotFound {
        return verifierDeploymentRepository.findById(id)
                .orElseThrow(() -> new DeploymentNotFound(DEPLOYMENT_NOT_FOUND_MSG));
    }

    private void validatePermissionToViewDeployment(VerifierDeployment deployment, User user) throws PermissionDeniedException {
        if (!deployment.getUser().getId().equals(user.getId()) && !user.getRole().equals(User.Role.ADMIN)) {
            throw new PermissionDeniedException(PERMISSION_DENIED_MSG);
        }
    }

    @Override
    @Transactional
    public void deleteDeploymentById(UUID id, User user) throws PermissionDeniedException, DeploymentNotFound {
        VerifierDeployment deployment = findDeploymentById(id);
        validatePermissionToDeleteDeployment(deployment, user);

        deployment.setStatus(Status.DELETED_SCHEDULED);
        verifierDeploymentRepository.save(deployment);
        eventPublisher.publishEvent(new VerifierDeploymentDeletedEvent(this, deployment));
    }

    private void validatePermissionToDeleteDeployment(VerifierDeployment deployment, User user) throws PermissionDeniedException {
        if (!deployment.getUser().getId().equals(user.getId()) && !user.getRole().equals(User.Role.ADMIN)) {
            throw new PermissionDeniedException(PERMISSION_DENIED_MSG);
        }
    }

    @Override
    @Transactional
    public UpdateClientSecretResponse updateClientSecret(UUID id, User user) throws PermissionDeniedException, DeploymentNotFound {
        VerifierDeployment deployment = findDeploymentById(id);
        validatePermissionToUpdateDeployment(deployment, user);

        String authKey = AuthKeyGenerator.generateAuthKey();
        eventPublisher.publishEvent(new VerifierDeploymentSecretUpgradeEvent(this, deployment, authKey));

        return new UpdateClientSecretResponse(authKey);
    }

    private void validatePermissionToUpdateDeployment(VerifierDeployment deployment, User user) throws PermissionDeniedException {
        if (!deployment.getUser().getId().equals(user.getId()) && !user.getRole().equals(User.Role.ADMIN)) {
            throw new PermissionDeniedException(PERMISSION_DENIED_UPDATE_MSG);
        }
    }

    @Override
    @Transactional
    public VerifierDeploymentDto upgradeDeployment(UUID verifierId, Long versionId, User user) throws DeploymentNotFound, VersionNotExistsExceptionBase, InvalidVersionUpgradeException, PermissionDeniedException {
        VerifierDeployment currentDeployment = findDeploymentById(verifierId);
        validatePermissionToViewDeployment(currentDeployment, user);

        String currentVersion = currentDeployment.getVerifierVersion().getVersion();
        VerifierVersion newVersion = findVersionToUpgrade(versionId);

        validateVersionUpgrade(currentVersion, newVersion.getVersion());

        currentDeployment.setVerifierVersion(newVersion);
        verifierDeploymentRepository.save(currentDeployment);

        eventPublisher.publishEvent(new VerifierDeploymentUpgradeEvent(this, currentDeployment));

        return verifierDeploymentMapper.toDto(currentDeployment);
    }

    private VerifierVersion findVersionToUpgrade(Long versionId) throws VersionNotExistsExceptionBase {
        return verifierVersionRepository.findById(versionId)
                .orElseThrow(() -> new VersionNotExistsExceptionBase(VERSION_TO_UPGRADE_NOT_EXISTS_MSG));
    }

    private void validateVersionUpgrade(String currentVersion, String newVersion) throws InvalidVersionUpgradeException {
        if (!isUpgradeValid(parseVersion(currentVersion), newVersion)) {
            throw new InvalidVersionUpgradeException(String.format(INVALID_VERSION_ERROR_MSG, currentVersion, newVersion));
        }
    }
}
