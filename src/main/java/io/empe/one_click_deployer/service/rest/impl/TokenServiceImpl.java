package io.empe.one_click_deployer.service.rest.impl;

import io.empe.one_click_deployer.entity.Token;
import io.empe.one_click_deployer.entity.User;
import io.empe.one_click_deployer.repository.TokenRepository;
import io.empe.one_click_deployer.repository.UserRepository;
import io.empe.one_click_deployer.service.rest.TokenService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

@Service
public class TokenServiceImpl implements TokenService {

    private static final SecureRandom secureRandom = new SecureRandom();
    private final TokenRepository tokenRepository;
    private final long confirmationTokenExpiry;
    private final long passwordResetTokenExpiry;
    private final int tokenRetryLimit;

    public TokenServiceImpl(TokenRepository tokenRepository,
                            UserRepository userRepository,
                            @Value("${application.token.confirmation.expiry-minutes:15}") long confirmationTokenExpiry,
                            @Value("${application.token.password-reset.expiry-minutes:15}") long passwordResetTokenExpiry,
                            @Value("${application.token.retry-limit:5}") int tokenRetryLimit) {
        this.tokenRepository = tokenRepository;
        this.confirmationTokenExpiry = confirmationTokenExpiry;
        this.passwordResetTokenExpiry = passwordResetTokenExpiry;
        this.tokenRetryLimit = tokenRetryLimit;
    }

    @Transactional
    public Token createToken(User user, Token.TokenType type) {
        tokenRepository.deleteByUserAndType(user, type);
        long expiryMinutes = type == Token.TokenType.EMAIL_CONFIRMATION ? confirmationTokenExpiry : passwordResetTokenExpiry;
        String generatedToken = String.format("%06d", secureRandom.nextInt(1_000_000));
        Token token = Token.builder()
                .token(generatedToken)
                .user(user)
                .type(type)
                .expiryDate(LocalDateTime.now().plusMinutes(expiryMinutes))
                .creationDate(LocalDateTime.now())
                .retryCount(0)
                .build();
        tokenRepository.save(token);
        return token;
    }

    @Transactional
    public boolean validateToken(UUID uuid, String token) {
        Optional<Token> tokenOptional = tokenRepository.findById(uuid);

        if (tokenOptional.isEmpty()) {
            return false;
        }

        Token foundToken = tokenOptional.get();

        if (foundToken.getExpiryDate().isBefore(LocalDateTime.now())) {
            tokenRepository.delete(foundToken);
            return false;
        }

        if (!foundToken.getToken().equals(token)) {
            foundToken.setRetryCount(foundToken.getRetryCount() + 1);
            if (foundToken.getRetryCount() >= tokenRetryLimit) {
                tokenRepository.delete(foundToken);
            } else {
                tokenRepository.save(foundToken);
            }
            return false;
        }

        return true;
    }

    @Transactional
    public void invalidateToken(String token) {
        tokenRepository.findByToken(token).ifPresent(tokenRepository::delete);
    }

    public User getUserByToken(String token) {
        return tokenRepository.findByToken(token).map(Token::getUser).orElse(null);
    }
}
