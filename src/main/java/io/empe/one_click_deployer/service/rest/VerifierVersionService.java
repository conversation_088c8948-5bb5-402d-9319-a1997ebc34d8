package io.empe.one_click_deployer.service.rest;

import io.empe.one_click_deployer.dto.CreateVerifierVersionRequest;
import io.empe.one_click_deployer.dto.VerifierVersionDto;
import io.empe.one_click_deployer.exception.external.VerifierVersionNotFoundException;

import java.util.List;

public interface VerifierVersionService {

    List<VerifierVersionDto> findAll();

    VerifierVersionDto findById(Long id) throws VerifierVersionNotFoundException;

    VerifierVersionDto create(CreateVerifierVersionRequest request);

    void delete(Long id) throws VerifierVersionNotFoundException;
}
