package io.empe.one_click_deployer.service.vault;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.vault.authentication.TokenAuthentication;
import org.springframework.vault.client.VaultEndpoint;
import org.springframework.vault.core.VaultOperations;
import org.springframework.vault.core.VaultSysOperations;
import org.springframework.vault.core.VaultTemplate;
import org.springframework.vault.support.VaultMount;
import org.springframework.vault.support.VaultToken;

import java.net.URI;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Service for interacting with HashiCorp Vault using Spring Vault.
 * Provides functionality for managing secrets, policies, and users for issuers.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class VaultService {

    @Value("${vault.addr:https://vault-stg.evdi.app}")
    private String vaultAddr;

    @Value("${vault.token}")
    private String rootToken;

    private VaultOperations getVaultOperations() {
        VaultEndpoint vaultEndpoint = VaultEndpoint.from(URI.create(vaultAddr));
        return new VaultTemplate(vaultEndpoint, new TokenAuthentication(VaultToken.of(rootToken)));
    }


    public VaultCredentials initializeVaultForIssuer(String issuerName) {
        log.info("Initializing Vault for issuer: {}", issuerName);

        try {
            String password = UUID.randomUUID().toString();

            VaultOperations vaultOperations = getVaultOperations();

            // Enable KV v2 secret engine if not already enabled
            enableSecretEngine(vaultOperations, issuerName);

            // Create policy for the issuer
            createPolicy(vaultOperations, issuerName);

            // Create user for the issuer
            createUser(vaultOperations, issuerName, password);

            log.info("Vault initialized successfully for issuer: {}", issuerName);

            // Return the Vault credentials
            return new VaultCredentials(vaultAddr, issuerName, password);
        } catch (Exception e) {
            log.error("Failed to initialize Vault for issuer: {}", issuerName, e);
            throw new RuntimeException("Failed to initialize Vault for issuer: " + issuerName, e);
        }
    }

    private void enableSecretEngine(VaultOperations vaultOperations, String issuerName) {
        log.debug("Enabling KV v2 secret engine for issuer: {}", issuerName);
        VaultSysOperations sysOperations = vaultOperations.opsForSys();

        // Check if the secret engine is already enabled
        if (!sysOperations.getMounts().containsKey(issuerName + "/")) {
            VaultMount mount = VaultMount.builder()
                    .type("kv")
                    .options(Collections.singletonMap("version", "2"))
                    .build();
            sysOperations.mount(issuerName, mount);
        }
    }

    private void createPolicy(VaultOperations vaultOperations, String issuerName) {
        log.debug("Creating policy for issuer: {}", issuerName);

        String policyName = issuerName + "-policy";
        String policyContent = String.format("""
                path "%s/data/*" {
                  capabilities = ["create", "read", "update", "delete", "list"]
                }
                """, issuerName);

        Map<String, Object> policyData = new HashMap<>();
        policyData.put("policy", policyContent);

        vaultOperations.write("sys/policies/acl/" + policyName, policyData);
    }

    private void createUser(VaultOperations vaultOperations, String issuerName, String password) {
        log.debug("Creating user for issuer: {}", issuerName);
        VaultSysOperations sysOperations = vaultOperations.opsForSys();

        // Enable userpass auth method if not already enabled
        try {
            if (!sysOperations.getAuthMounts().containsKey("userpass/")) {
                sysOperations.authMount("userpass", VaultMount.builder().type("userpass").build());
            }
        } catch (Exception e) {
            log.debug("Userpass auth method might already be enabled: {}", e.getMessage());
        }

        // Create user with password and policy
        Map<String, Object> userConfig = new HashMap<>();
        userConfig.put("password", password);
        userConfig.put("policies", issuerName + "-policy");

        vaultOperations.write("auth/userpass/users/" + issuerName, userConfig);
    }
}
