package io.empe.one_click_deployer.service.issuer_run.impl;

import io.empe.one_click_deployer.dto.CounterTypesDto;
import io.empe.one_click_deployer.dto.CounterUpdateDto;
import io.empe.one_click_deployer.entity.IssuerDeployment;
import io.empe.one_click_deployer.entity.Metadata;
import io.empe.one_click_deployer.entity.User;
import io.empe.one_click_deployer.entity.UserSubscription;
import io.empe.one_click_deployer.exception.external.SetIssuerLimitsError;
import io.empe.one_click_deployer.repository.UserSubscriptionRepository;
import io.empe.one_click_deployer.service.issuer_run.IssuerRunService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class IssuerRunServiceImpl implements IssuerRunService {
    @Value("${issuer.admin-secret}")
    private String adminSecret;

    private final RestTemplate restTemplate;
    private final UserSubscriptionRepository userSubscriptionRepository;

    public void resetAllLimitsBased(IssuerDeployment issuerDeployment) throws SetIssuerLimitsError {
        log.info("Resetting all limits for issuer: {}", issuerDeployment.getId());
        Set<Metadata> metadataSet = getMetadataForDeployment(issuerDeployment.getUser());

        List<CounterUpdateDto> limits = metadataSet.stream()
                .map(meta -> CounterTypesDto.fromIssuerKey(meta.getType())
                        .map(type -> buildCounterUpdateDto(type, meta.getValue(), true))
                        .orElse(null))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        log.info("Limits to be reset for issuer {}: {}", issuerDeployment.getId(), limits);
        if (!limits.isEmpty()) {
            this.setLimits(issuerDeployment, adminSecret, limits);
        }
    }

    private Set<Metadata> getMetadataForDeployment(User user) throws SetIssuerLimitsError {
        Optional<UserSubscription> sub = userSubscriptionRepository
                .findByUserAndSubscriptionActiveTrue(user);

        if (sub.isEmpty()) {
            throw new SetIssuerLimitsError("User subscription not found for user: " + user.getId());
        }

        return sub.get().getMetadata();
    }

    // Helper method to build CounterUpdateDto from CounterTypesDto and value
    private CounterUpdateDto buildCounterUpdateDto(CounterTypesDto type, String value, Boolean resetUsed) {
        Integer cyclical_limit = null;
        if (value != null && !value.isBlank()) {
            try {
                cyclical_limit = Integer.valueOf(value);
            } catch (NumberFormatException ex) {
                log.warn("Invalid value for {}: '{}'. Expected an integer.", type.name(), value);
            }
        }

        return CounterUpdateDto.builder()
                .name(type.name())
                .cyclicalLimit(cyclical_limit)
                .resetUsed(resetUsed)
                .additionalLimit(null)
                .build();
    }

    /**
     * Wysyła batch-update limitów do konkretniego Issuera.
     *
     * @param issuerDeployment kontekst Issuera (URL, status, itp.)
     * @param adminSecret      tajny nagłówek „x-client-secret”
     * @param limits           lista zmian (możesz przekazać 1..N wpisów)
     */
    private void setLimits(IssuerDeployment issuerDeployment,
                           String adminSecret,
                           List<CounterUpdateDto> limits) throws SetIssuerLimitsError {

        final String apiUrl = issuerDeployment.getFullHost() + "/api/v1/counters/batch-update";

        try {
            log.debug("Sending POST {} with {} updates", apiUrl, limits.size());

            HttpHeaders headers = new HttpHeaders();
            headers.set("x-admin-secret", adminSecret);
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<List<CounterUpdateDto>> entity = new HttpEntity<>(limits, headers);
            ResponseEntity<Void> response = restTemplate.postForEntity(apiUrl, entity, Void.class);

            if (!response.getStatusCode().is2xxSuccessful()) {
                log.warn("Failed to send POST {} with {} updates", apiUrl, limits.size());
                throw new SetIssuerLimitsError("Limits updated successfully for issuer: " + issuerDeployment.getId());
            }


            log.info("Limits updated successfully for issuer: {}", issuerDeployment.getId());
        } catch (Exception ex) {
            log.error("Exception while updating limits: {}", ex.getMessage(), ex);
            throw new SetIssuerLimitsError("Failed to update limits for issuer: " + issuerDeployment.getId() +
                    ". Error: " + ex.getMessage());
        }

    }

}
