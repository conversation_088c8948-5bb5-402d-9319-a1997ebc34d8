package io.empe.one_click_deployer.service.deployment.impl;

import io.empe.one_click_deployer.entity.IssuerDeployment;
import io.empe.one_click_deployer.repository.IssuerDeploymentRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.math.BigInteger;
import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class TokenUpService {

    private final IssuerDeploymentRepository issuerDeploymentRepository;
    private final RestTemplate restTemplate;

    private static final BigInteger TOKEN_LOW_WATERMARK_TOKEN  = new BigInteger("********");
    private static final BigInteger TOKEN_HIGH_WATERMARK_TOKEN = new BigInteger("********");
    private static final String DENOM = "uempe";

    /**
     * Runs at the interval configured in application properties.
     * For every IssuerDeployment it checks the balance; if the balance is
     * below LOW_WATERMARK it tops the account up to HIGH_WATERMARK.
     * Deployments are grouped by faucet (vset) address so that each faucet
     * receives a single request containing only its own accounts.
     */
    @Scheduled(cron = "${tokenup.cronInterval:0 */5 * * * *}")
    @Transactional
    public void topUpBalances() {
        log.debug("Starting token top-up process…");

        // 1. Gather accounts that need tokens, keyed by faucet URL
        Map<String, List<Map<String, String>>> accountsByFaucet = new HashMap<>();

        issuerDeploymentRepository.findAll().forEach(deployment -> {
            String address = deployment.getIssuerCreatorAddress();
            if (address == null) return;

            String lcd = deployment.getBlockchainConfiguration().getLcd();
            BigInteger balance = getBalance(lcd, address);
            if (balance.compareTo(TOKEN_LOW_WATERMARK_TOKEN) >= 0) return; // balance is sufficient

            BigInteger amount = TOKEN_HIGH_WATERMARK_TOKEN.subtract(balance);
            String faucet   = deployment.getBlockchainConfiguration().getFaucetUrl();

            accountsByFaucet
                    .computeIfAbsent(faucet, k -> new ArrayList<>())
                    .add(Map.of("address", address, "amount", amount.toString()));
        });

        if (accountsByFaucet.isEmpty()) {
            log.debug("No accounts need a top-up.");
            return;
        }

        // 2. Send one request per faucet
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        accountsByFaucet.forEach((faucetUrl, accounts) -> {
            HttpEntity<Map<String, List<Map<String, String>>>> request =
                    new HttpEntity<>(Map.of("accounts", accounts), headers);

            try {
                restTemplate.postForEntity(faucetUrl + "/faucet/send", request, String.class);
                log.debug("Token top-up sent to faucet {} for {} accounts", faucetUrl, accounts.size());
            } catch (Exception e) {
                log.error("Token top-up to faucet {} failed: {}", faucetUrl, e.getMessage(), e);
            }
        });
    }

    /**
     * Returns the current balance (in uempe) for the given address,
     * or BigInteger.ZERO if the balance cannot be fetched or parsed.
     */
    private BigInteger getBalance(String lcdBaseUrl, String address) {
        String url = lcdBaseUrl + "cosmos/bank/v1beta1/spendable_balances/" + address;
        try {
            var resp = restTemplate.getForEntity(url, Map.class);
            if (!resp.getStatusCode().is2xxSuccessful() || resp.getBody() == null) return BigInteger.ZERO;

            Object balancesObj = resp.getBody().get("balances");
            if (balancesObj instanceof List<?> list) {
                for (Object o : list) {
                    if (o instanceof Map<?, ?> m
                            && DENOM.equals(m.get("denom"))
                            && m.get("amount") instanceof String s) {
                        return new BigInteger(s);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("Could not fetch balance for {}: {}", address, e.getMessage());
        }
        return BigInteger.ZERO;
    }
}