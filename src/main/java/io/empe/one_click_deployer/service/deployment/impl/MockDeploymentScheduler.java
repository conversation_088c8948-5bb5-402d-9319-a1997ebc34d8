package io.empe.one_click_deployer.service.deployment.impl;

import io.empe.one_click_deployer.entity.IssuerDeployment;
import io.empe.one_click_deployer.entity.Status;
import io.empe.one_click_deployer.entity.VerifierDeployment;
import io.empe.one_click_deployer.mapper.IssuerDeploymentMapper;
import io.empe.one_click_deployer.repository.IssuerDeploymentRepository;
import io.empe.one_click_deployer.repository.VerifierDeploymentRepository;
import io.empe.one_click_deployer.service.deployment.DeploymentScheduler;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service("EMPE_MOCK_DEPLOYMENT")
@Slf4j
@AllArgsConstructor
public class MockDeploymentScheduler implements DeploymentScheduler {

    private final IssuerDeploymentMapper issuerDeploymentMapper = IssuerDeploymentMapper.INSTANCE;
    private IssuerDeploymentRepository issuerDeploymentRepository;
    private VerifierDeploymentRepository verifierDeploymentRepository; // Inject VerifierDeploymentRepository

    @Override
    @Async
    public void scheduleDeployment(IssuerDeployment issuerDeployment, String authKey) {
        log.info("Scheduling Mock deployment for issuer: {}", issuerDeployment.getIssuerName());
        try {
            issuerDeployment.setStatus(Status.ACTIVE);
            issuerDeploymentRepository.save(issuerDeployment);
            log.info("Deployment scheduled successfully for issuer: {}", issuerDeployment.getIssuerName());
        } catch (Exception e) {
            log.error("Error scheduling deployment for issuer: {}: {}", issuerDeployment.getIssuerName(), e.getMessage());
        }
    }

    @Override
    @Async
    public void deleteDeployment(IssuerDeployment issuerDeployment) {
        log.info("Deleting Mock deployment for issuer: {}", issuerDeployment.getIssuerName());
        try {
            issuerDeployment.setStatus(Status.DELETED_SCHEDULED);
            issuerDeploymentRepository.save(issuerDeployment);
            log.info("Deployment marked for deletion for issuer: {}", issuerDeployment.getIssuerName());
        } catch (Exception e) {
            log.error("Error deleting deployment for issuer: {}: {}", issuerDeployment.getIssuerName(), e.getMessage());
        }
    }

    @Override
    @Async
    public void scheduleDeployment(VerifierDeployment verifierDeployment, String authKey) {
        log.info("Scheduling Mock deployment for verifier: {}", verifierDeployment.getVerifierName());
        try {
            verifierDeployment.setStatus(Status.ACTIVE);
            verifierDeploymentRepository.save(verifierDeployment);  // Save the VerifierDeployment
            log.info("Deployment scheduled successfully for verifier: {}", verifierDeployment.getVerifierName());
        } catch (Exception e) {
            log.error("Error scheduling deployment for verifier: {}: {}", verifierDeployment.getVerifierName(), e.getMessage());
        }
    }

    @Override
    @Async
    public void deleteDeployment(VerifierDeployment verifierDeployment) {
        log.info("Deleting Mock deployment for verifier: {}", verifierDeployment.getVerifierName());
        try {
            verifierDeployment.setStatus(Status.DELETED_SCHEDULED);
            verifierDeploymentRepository.save(verifierDeployment);  // Save the VerifierDeployment
            log.info("Deployment marked for deletion for verifier: {}", verifierDeployment.getVerifierName());
        } catch (Exception e) {
            log.error("Error deleting deployment for verifier: {}: {}", verifierDeployment.getVerifierName(), e.getMessage());
        }
    }

    @Override
    public void scheduleClientSecretUpgrade(IssuerDeployment issuerDeployment, String authKey) {

    }

    @Override
    public void scheduleClientSecretUpgrade(VerifierDeployment verifierDeployment, String authKey) {

    }

    @Override
    public void scheduleUpgrade(IssuerDeployment deployment) {

    }

    @Override
    public void scheduleUpgrade(VerifierDeployment deployment) {

    }
}
