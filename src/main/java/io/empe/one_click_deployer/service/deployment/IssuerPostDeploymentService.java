package io.empe.one_click_deployer.service.deployment;

import io.empe.one_click_deployer.dto.external.CaskResponse;
import io.empe.one_click_deployer.dto.external.IssuerCreatorAddress;
import io.empe.one_click_deployer.entity.IssuerDeployment;
import io.empe.one_click_deployer.entity.Status;
import io.empe.one_click_deployer.exception.external.SetIssuerLimitsError;
import io.empe.one_click_deployer.repository.IssuerDeploymentRepository;
import io.empe.one_click_deployer.service.issuer_run.IssuerRunService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Map;
import java.util.Optional;

import static java.lang.Thread.sleep;

@Service
@RequiredArgsConstructor
@Slf4j
public class IssuerPostDeploymentService {

    private final RestTemplate restTemplate;
    private final IssuerDeploymentRepository issuerDeploymentRepository;
    private final IssuerRunService issuerRunService;

    @Value("${faucet.url}")
    private String faucetUrl;

    public String fetchIssuerCreatorAddress(IssuerDeployment issuerDeployment, String clientSecret) {
        String url = issuerDeployment.getFullHost() + "/api/v1/blockchain/address";

        try {
            log.info("Waiting for issuer creator address to be fetched...");
            sleep(5000);
            log.debug("Sending GET request to URL: {}", url);

            // Create HTTP headers with the `x-client-secret`
            HttpHeaders headers = new HttpHeaders();
            headers.set("x-client-secret", clientSecret);

            // Create an HTTP entity with headers
            HttpEntity<String> entity = new HttpEntity<>(headers);

            // Execute the GET request with headers
            ResponseEntity<IssuerCreatorAddress> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    entity,
                    IssuerCreatorAddress.class
            );

            return Optional.ofNullable(response.getBody()).map(body -> {
                String address = body.getAddress();
                log.info("Received blockchain address for Issuer {}: {}", issuerDeployment.getIssuerName(), address);

                issuerDeployment.setIssuerCreatorAddress(address);
                issuerDeploymentRepository.save(issuerDeployment);
                return address;
            }).orElseGet(() -> {
                log.error("Failed to fetch blockchain address. Response body is null.");
                issuerDeployment.setStatus(Status.FAILED);
                issuerDeployment.setFailReason("Failed to fetch blockchain address. Response body is null.");
                issuerDeploymentRepository.save(issuerDeployment);
                return null;
            });
        } catch (Exception e) {
            log.error("Exception occurred while fetching blockchain address for Issuer {}: {}", issuerDeployment.getIssuerName(), e.getMessage(), e);
            issuerDeployment.setStatus(Status.FAILED);
            issuerDeployment.setFailReason("Exception while fetching blockchain address: " + e.getMessage());
            issuerDeploymentRepository.save(issuerDeployment);
            return null;
        }
    }

    public String postToFaucetWithRetry(IssuerDeployment issuerDeployment) {
        String url = issuerDeployment.getBlockchainConfiguration().getFaucetUrl() + "/faucet/address";
        int maxRetries = 2;
        int attempt = 0;

        Map<String, String> requestBody = Map.of("address", issuerDeployment.getIssuerCreatorAddress());

        while (attempt <= maxRetries) {
            try {
                log.info("Attempt {}: Sending POST request to URL: {} with address: {}", attempt + 1, url, requestBody.get("address"));

                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);

                HttpEntity<Map<String, String>> request = new HttpEntity<>(requestBody, headers);

                ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

                if (response.getStatusCode().is2xxSuccessful()) {
                    log.info("Successfully posted to faucet for address: {}", requestBody.get("address"));
                    issuerDeployment.setStatus(Status.TOKEN_ON_ISSUER);
                    issuerDeploymentRepository.save(issuerDeployment);
                    return response.getBody();
                } else {
                    log.error("Failed to post to faucet. Status code: {}", response.getStatusCode());
                }
            } catch (Exception e) {
                log.error("Exception occurred while posting to faucet for address {}: {}", requestBody.get("address"), e.getMessage(), e);
            }
            attempt++;
            if (attempt <= maxRetries) {
                log.info("Retrying... (Attempt {}/{})", attempt + 1, maxRetries + 1);
            }
        }
        log.error("All attempts to post to faucet failed for address: {}", requestBody.get("address"));
        issuerDeployment.setStatus(Status.FAILED);
        issuerDeployment.setFailReason("Failed to post to faucet after multiple attempts.");
        issuerDeploymentRepository.save(issuerDeployment);
        return null;
    }


    public CaskResponse caskOnBlockchain(IssuerDeployment issuerDeployment, String clientSecret) {
        String apiUrl = issuerDeployment.getFullHost() + "/api/v1/blockchain/dids";
        String address = issuerDeployment.getIssuerCreatorAddress();

        try {
            log.debug("Sending POST request to URL: {} with address: {}", apiUrl, address);

            // Create HTTP headers with the `x-client-secret`
            HttpHeaders headers = new HttpHeaders();
            headers.set("x-client-secret", clientSecret);

            // Create an HTTP entity with headers and the address as the body
            HttpEntity<String> entity = new HttpEntity<>(address, headers);

            // Execute the POST request with headers
            ResponseEntity<CaskResponse> response = restTemplate.postForEntity(apiUrl, entity, CaskResponse.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Successfully posted to blockchain for address: {}", address);
                return response.getBody();
            } else {
                log.error("Failed to post to blockchain. Status code: {}", response.getStatusCode());
                issuerDeployment.setStatus(Status.FAILED);
                issuerDeployment.setFailReason("Failed to post to blockchain. Status code: " + response.getStatusCode());
                issuerDeploymentRepository.save(issuerDeployment);
                return null;
            }
        } catch (Exception e) {
            log.error("Exception occurred while posting to blockchain for address {}: {}", address, e.getMessage(), e);
            issuerDeployment.setStatus(Status.FAILED);
            issuerDeployment.setFailReason("Exception while posting to blockchain: " + e.getMessage());
            issuerDeploymentRepository.save(issuerDeployment);
            return null;
        }
    }


    public String verifyCaskOnBlockchain(IssuerDeployment issuerDeployment, String clientSecret, CaskResponse blockchainResponse) {
        String apiUrl = issuerDeployment.getFullHost() + "/api/v1/blockchain/dids/" + blockchainResponse.getDid();
        String address = issuerDeployment.getIssuerCreatorAddress();

        try {
            log.debug("Sending GET request to URL: {} with address: {}", apiUrl, address);

            // Create HTTP headers with the `x-client-secret`
            HttpHeaders headers = new HttpHeaders();
            headers.set("x-client-secret", clientSecret);

            // Create an HTTP entity with headers
            HttpEntity<String> entity = new HttpEntity<>(headers);

            // Execute the GET request with headers
            ResponseEntity<String> response = restTemplate.exchange(
                    apiUrl,
                    HttpMethod.GET,
                    entity,
                    String.class
            );

            if (response.getStatusCode().is2xxSuccessful()) {
                String didDocumentJson = response.getBody();
                log.info("Verification successful for address: {}. DID Document: {}", address, didDocumentJson);

                // Save the DID Document JSON to the IssuerDeployment
                issuerDeployment.setDidDocument(didDocumentJson);
                issuerDeploymentRepository.save(issuerDeployment);

                return didDocumentJson;
            } else {
                log.error("Verification failed. Status code: {}", response.getStatusCode());
                return null;
            }
        } catch (Exception e) {
            log.error("Exception occurred during verification for address {}: {}", address, e.getMessage(), e);
            return null;
        }
    }


    public boolean retryCaskVerification(IssuerDeployment issuerDeployment, String clientSecret, CaskResponse blockchainResponse) {
        int maxRetries = 5;
        int attempt = 0;

        while (attempt < maxRetries) {
            String didDocumentJson = verifyCaskOnBlockchain(issuerDeployment, clientSecret, blockchainResponse);
            if (didDocumentJson != null) {
                return true;
            }
            attempt++;
            log.info("Retrying verification... (Attempt {}/{})", attempt + 1, maxRetries);
        }
        log.error("All attempts to verify cask on blockchain failed for address: {}", issuerDeployment.getIssuerCreatorAddress());
        return false;
    }

    public void executePostDeploymentProcess(IssuerDeployment issuerDeployment, String clientSecret) {
        try {
            sleep(20000); // 5-second delay wait on block to be commited

            // Step 1: Fetch Issuer Creator Address
            String address = fetchIssuerCreatorAddress(issuerDeployment, clientSecret);
            if (address == null) {
                log.error("Failed to fetch issuer creator address. Aborting process.");
                return;
            }

            // Step 2: Post to Faucet
            String faucetResponse = postToFaucetWithRetry(issuerDeployment);
            if (faucetResponse == null) {
                log.error("Failed to post to faucet. Aborting process.");
                return;
            }
            sleep(5000); // 5-second delay wait on block to be commited

            // Step 3: Cask on Blockchain
            CaskResponse blockchainResponse = caskOnBlockchain(issuerDeployment, clientSecret);
            if (blockchainResponse == null) {
                log.error("Failed to cask on blockchain. Aborting process.");
                return;
            }
            log.info("Blockchain response: {}", blockchainResponse);
            sleep(5000); // 5-second delay wait on block to be commited

            // Step 4: Verify Cask on Blockchain
            boolean isVerified = retryCaskVerification(issuerDeployment, clientSecret, blockchainResponse);
            if (!isVerified) {
                log.error("Failed to verify cask on blockchain. Aborting process.");
                return;
            }
            log.info("Cask verification successful for address: {}", address);

            // Step 5: Reset all limits based on the issuer deployment
            issuerRunService.resetAllLimitsBased(issuerDeployment);

            // Update status to SUCCESS after successful completion
            issuerDeployment.setStatus(Status.ACTIVE);
            issuerDeploymentRepository.save(issuerDeployment);

            log.info("Deployment process completed successfully for address: {}", address);
        } catch (InterruptedException | SetIssuerLimitsError e) {
            log.error("Process was interrupted: {}", e.getMessage(), e);
            issuerDeployment.setStatus(Status.FAILED);
            issuerDeployment.setFailReason("Process was interrupted: " + e.getMessage());
            issuerDeploymentRepository.save(issuerDeployment);
            Thread.currentThread().interrupt();
        }
    }
} 