package io.empe.one_click_deployer.service.deployment;

import io.empe.one_click_deployer.entity.IssuerDeployment;
import io.empe.one_click_deployer.entity.VerifierDeployment;

public interface DeploymentScheduler {
    void scheduleDeployment(IssuerDeployment issuerDeployment, String authKey);

    void deleteDeployment(IssuerDeployment issuerDeployment);

    void scheduleDeployment(VerifierDeployment verifierDeployment, String authKey);

    void deleteDeployment(VerifierDeployment verifierDeployment);

    // Methods for upgrading client secrets
    void scheduleClientSecretUpgrade(IssuerDeployment issuerDeployment, String authKey);

    void scheduleClientSecretUpgrade(VerifierDeployment verifierDeployment, String authKey);

    void scheduleUpgrade(IssuerDeployment deployment);

    void scheduleUpgrade(VerifierDeployment deployment);
}
