package io.empe.one_click_deployer.service.deployment.impl;

import com.marcnuri.helm.Helm;
import com.marcnuri.helm.InstallCommand;
import com.marcnuri.helm.Release;
import com.marcnuri.helm.UpgradeCommand;
import io.empe.one_click_deployer.entity.*;
import io.empe.one_click_deployer.repository.IssuerDeploymentRepository;
import io.empe.one_click_deployer.repository.UserSubscriptionRepository;
import io.empe.one_click_deployer.repository.VerifierDeploymentRepository;
import io.empe.one_click_deployer.service.database.PostgreSQLCredentials;
import io.empe.one_click_deployer.service.database.PostgreSQLInitializer;
import io.empe.one_click_deployer.service.deployment.DeploymentScheduler;
import io.empe.one_click_deployer.service.deployment.IssuerPostDeploymentService;
import io.empe.one_click_deployer.service.vault.VaultCredentials;
import io.empe.one_click_deployer.service.vault.VaultService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.net.URL;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

@Service("EMPE_OVH_K8S_DEPLOYMENT")
@RequiredArgsConstructor
public class OvhK8SDeploymentScheduler implements DeploymentScheduler {

    private static final Logger logger = LoggerFactory.getLogger(OvhK8SDeploymentScheduler.class);
    private final CloudflareDNSUpdaterService cloudflareDNSUpdaterService;
    private final IssuerDeploymentRepository issuerDeploymentRepository;
    private final VerifierDeploymentRepository verifierDeploymentRepository;
    private final IssuerPostDeploymentService issuerPostDeploymentService;
    private final PostgreSQLInitializer postgreSQLInitializer;
    private final UserSubscriptionRepository userSubscriptionRepository;
    private final VaultService vaultService;

    @Value("${kubernetes.config.path}")
    private String kubeConfigPath;
    @Value("${one-click.k8s-namespace}")
    private String namespace;
    @Value("${issuer.admin-secret}")
    private String adminSecret;

    @Value("${one-click.istio-ip}")
    private String istioIp;

    // Scheduling method for IssuerDeployment
    @Override
    public void scheduleDeployment(IssuerDeployment issuerDeployment, String authKey) {
        logger.info("Scheduling OVH K8S deployment for Issuer: {}", issuerDeployment.getIssuerName());

        PostgreSQLCredentials credentials;
        try {
            logger.info("Initializing PostgreSQL database for Issuer: {}", issuerDeployment.getIssuerName());
            credentials = postgreSQLInitializer.initializeDatabase(issuerDeployment.getIssuerName());
            logger.info("PostgreSQL database initialized successfully for Issuer: {}", issuerDeployment.getIssuerName());
        } catch (Exception e) {
            logger.error("Failed to initialize PostgreSQL database for Issuer: {}", issuerDeployment.getIssuerName(), e);
            handleDeploymentFailure(issuerDeployment, e);
            return;
        }

//        var vaultCredentials = vaultService.initializeVaultForIssuer(issuerDeployment.getIssuerName());

        try {
            Map<String, String> values = buildIssuerValuesMap(issuerDeployment, authKey);
            addPostgreSQLCredentials(values, credentials);
//            addVaultCredentials(values, vaultCredentials);
            Release result = installIssuerHelmChart(issuerDeployment, values);
            logger.info("Helm deployment result: {}", result);
        } catch (Exception e) {
            handleDeploymentFailure(issuerDeployment, e);
            return;
        }

        try {
            // Update DNS record for Issuer
            cloudflareDNSUpdaterService.addDNSRecord("A", String.format("%s.evdi.app", issuerDeployment.getIssuerName()), istioIp, 120, true);
        } catch (Exception e) {
            handleDNSFailure(issuerDeployment, e);
            return;
        }

        // Update status to READY_TO_PUT_TO_BLOCKCHAIN after successful deployment
        issuerDeployment.setStatus(Status.READY_TO_PUT_TO_BLOCKCHAIN);
        issuerDeploymentRepository.save(issuerDeployment);

        issuerPostDeploymentService.executePostDeploymentProcess(issuerDeployment, authKey);
    }

    // Scheduling method for VerifierDeployment
    @Override
    public void scheduleDeployment(VerifierDeployment verifierDeployment, String authKey) {
        logger.info("Scheduling OVH K8S deployment for Verifier: {}", verifierDeployment.getVerifierName());

        PostgreSQLCredentials credentials;
        try {
            logger.info("Initializing PostgreSQL database for Verifier: {}", verifierDeployment.getVerifierName());
            credentials = postgreSQLInitializer.initializeDatabase(verifierDeployment.getVerifierName());
            System.out.printf(credentials.toString());
            logger.info("PostgreSQL database initialized successfully for Verifier: {}", verifierDeployment.getVerifierName());
        } catch (Exception e) {
            logger.error("Failed to initialize PostgreSQL database for Verifier: {}", verifierDeployment.getVerifierName(), e);
            handleDeploymentFailure(verifierDeployment, e);
            return;
        }


        try {
            Map<String, String> values = buildVerifierValuesMap(verifierDeployment, authKey);
            addPostgreSQLCredentials(values, credentials);
            Release result = installVerifierHelmChart(verifierDeployment, values);
            logger.info("Helm deployment result: {}", result);
        } catch (Exception e) {
            handleDeploymentFailure(verifierDeployment, e);
            return;
        }

        try {
            // Update DNS record for Verifier
            cloudflareDNSUpdaterService.addDNSRecord("A", String.format("%s.evdi.app", verifierDeployment.getVerifierName()), istioIp, 120, true);
        } catch (Exception e) {
            handleDNSFailure(verifierDeployment, e);
            return;
        }

        // Update status to ACTIVE after successful deployment
        verifierDeployment.setStatus(Status.ACTIVE);
        verifierDeploymentRepository.save(verifierDeployment);
    }

    private Set<Metadata> getMetadataForDeployment(User user) {
        Optional<UserSubscription> sub = userSubscriptionRepository
                .findByUserAndSubscriptionActiveTrue(user);

        if (sub.isEmpty()) {
            throw new UsernameNotFoundException("User not found");
        }

        return sub.get().getMetadata();
    }

    // Deleting IssuerDeployment
    @Override
    public void deleteDeployment(IssuerDeployment issuerDeployment) {
        try {
            // Delete DNS record for Issuer
            cloudflareDNSUpdaterService.deleteDNSRecordByName(String.format("%s.evdi.app", issuerDeployment.getIssuerName()));
        } catch (Exception e) {
            handleDNSDeletionFailure(issuerDeployment, e);
            return;
        }

        try {
            // Uninstall Helm chart for Issuer
            String result = Helm.uninstall(issuerDeployment.getIssuerName())
                    .withNamespace(namespace)
                    .withKubeConfig(Paths.get(kubeConfigPath))
                    .debug()
                    .call();
            logger.info("Helm uninstallation result: {}", result);
        } catch (Exception e) {
            handleUninstallationFailure(issuerDeployment, e);
            return;
        }

        // Delete IssuerDeployment from repository
        issuerDeploymentRepository.delete(issuerDeployment);
    }

    // Deleting VerifierDeployment
    @Override
    public void deleteDeployment(VerifierDeployment verifierDeployment) {
        try {
            // Delete DNS record for Verifier
            cloudflareDNSUpdaterService.deleteDNSRecordByName(String.format("%s.evdi.app", verifierDeployment.getVerifierName()));
        } catch (Exception e) {
            handleDNSDeletionFailure(verifierDeployment, e);
            return;
        }

        try {
            // Uninstall Helm chart for Verifier
            String result = Helm.uninstall(verifierDeployment.getVerifierName())
                    .withNamespace(namespace)
                    .withKubeConfig(Paths.get(kubeConfigPath))
                    .debug()
                    .call();
            logger.info("Helm uninstallation result: {}", result);
        } catch (Exception e) {
            handleUninstallationFailure(verifierDeployment, e);
            return;
        }

        // Delete VerifierDeployment from repository
        verifierDeploymentRepository.delete(verifierDeployment);
    }

    // Helper method for handling deployment failure for IssuerDeployment
    private void handleDeploymentFailure(IssuerDeployment issuerDeployment, Exception e) {
        logger.error("Error occurred during Helm chart installation for {}", issuerDeployment.getIssuerName(), e);
        issuerDeployment.setStatus(Status.FAILED);
        issuerDeployment.setFailReason("Helm chart installation failed: " + e.getMessage());
        issuerDeploymentRepository.save(issuerDeployment);
    }

    // Helper method for handling deployment failure for VerifierDeployment
    private void handleDeploymentFailure(VerifierDeployment verifierDeployment, Exception e) {
        logger.error("Error occurred during Helm chart installation for {}: {}", verifierDeployment.getVerifierName(), e.getMessage());
        verifierDeployment.setStatus(Status.FAILED);
        verifierDeployment.setFailReason("Helm chart installation failed: " + e.getMessage());
        verifierDeploymentRepository.save(verifierDeployment);
    }

    // Helper method for handling DNS failure for IssuerDeployment
    private void handleDNSFailure(IssuerDeployment issuerDeployment, Exception e) {
        logger.error("Failed to add DNS record for {}: {}", issuerDeployment.getIssuerName(), e.getMessage());
        issuerDeployment.setStatus(Status.FAILED);
        issuerDeployment.setFailReason("DNS record addition failed: " + e.getMessage());
        issuerDeploymentRepository.save(issuerDeployment);
    }

    // Helper method for handling DNS failure for VerifierDeployment
    private void handleDNSFailure(VerifierDeployment verifierDeployment, Exception e) {
        logger.error("Failed to add DNS record for {}: {}", verifierDeployment.getVerifierName(), e.getMessage());
        verifierDeployment.setStatus(Status.FAILED);
        verifierDeployment.setFailReason("DNS record addition failed: " + e.getMessage());
        verifierDeploymentRepository.save(verifierDeployment);
    }

    // Helper method for handling DNS deletion failure for IssuerDeployment
    private void handleDNSDeletionFailure(IssuerDeployment issuerDeployment, Exception e) {
        logger.error("Failed to delete DNS record for {}: {}", issuerDeployment.getIssuerName(), e.getMessage());
        issuerDeployment.setStatus(Status.FAILED_DELETION);
        issuerDeployment.setFailReason("DNS record deletion failed: " + e.getMessage());
        issuerDeploymentRepository.save(issuerDeployment);
    }

    // Helper method for handling DNS deletion failure for VerifierDeployment
    private void handleDNSDeletionFailure(VerifierDeployment verifierDeployment, Exception e) {
        logger.error("Failed to delete DNS record for {}: {}", verifierDeployment.getVerifierName(), e.getMessage());
        verifierDeployment.setStatus(Status.FAILED_DELETION);
        verifierDeployment.setFailReason("DNS record deletion failed: " + e.getMessage());
        verifierDeploymentRepository.save(verifierDeployment);
    }

    // Helper method for handling uninstallation failure for IssuerDeployment
    private void handleUninstallationFailure(IssuerDeployment issuerDeployment, Exception e) {
        logger.error("Error occurred during Helm chart uninstallation for {}: {}", issuerDeployment.getIssuerName(), e.getMessage());
        issuerDeployment.setStatus(Status.FAILED_DELETION);
        issuerDeployment.setFailReason("Helm chart uninstallation failed: " + e.getMessage());
        issuerDeploymentRepository.save(issuerDeployment);
    }

    // Helper method for handling uninstallation failure for VerifierDeployment
    private void handleUninstallationFailure(VerifierDeployment verifierDeployment, Exception e) {
        logger.error("Error occurred during Helm chart uninstallation for {}: {}", verifierDeployment.getVerifierName(), e.getMessage());
        verifierDeployment.setStatus(Status.FAILED_DELETION);
        verifierDeployment.setFailReason("Helm chart uninstallation failed: " + e.getMessage());
        verifierDeploymentRepository.save(verifierDeployment);
    }

    // Install Helm chart for IssuerDeployment
    private Release installIssuerHelmChart(IssuerDeployment issuerDeployment, Map<String, String> values) throws Exception {
        URL resource = getClass().getClassLoader().getResource("issuer-sdk-helm");
        if (resource == null) {
            throw new IllegalArgumentException("Helm chart path not found in resources");
        }
        Path helmChartPath = Paths.get(resource.toURI());
        InstallCommand installCommand = new Helm(helmChartPath).install();
        values.forEach(installCommand::set);

        return installCommand
                .withName(issuerDeployment.getIssuerName())
                .withNamespace(namespace)
                .waitReady()
                .withKubeConfig(Paths.get(kubeConfigPath))
                .debug()
                .call();
    }

    // Install Helm chart for VerifierDeployment
    private Release installVerifierHelmChart(VerifierDeployment verifierDeployment, Map<String, String> values) throws Exception {
        URL resource = getClass().getClassLoader().getResource("verifier-sdk-helm");
        if (resource == null) {
            throw new IllegalArgumentException("Helm chart path not found in resources");
        }
        Path helmChartPath = Paths.get(resource.toURI());
        InstallCommand installCommand = new Helm(helmChartPath).install();
        values.forEach(installCommand::set);

        return installCommand
                .withName(verifierDeployment.getVerifierName())
                .withNamespace(namespace)
                .waitReady()
                .withKubeConfig(Paths.get(kubeConfigPath))
                .debug()
                .call();
    }

    // Build values map for IssuerDeployment
    private Map<String, String> buildIssuerValuesMap(IssuerDeployment issuerDeployment, String authKey) {
        Map<String, String> values = new HashMap<>();
        String domain = String.format("%s.evdi.app", issuerDeployment.getIssuerName());

        values.put("replicaCount", "1");
        values.put("uniqueName", issuerDeployment.getIssuerName());

        values.put("image.repository", "309596z9.c1.gra9.container-registry.ovh.net/empe/services/issuer-sdk");
        values.put("image.tag", issuerDeployment.getIssuerVersion().getVersion());
        values.put("image.pullPolicy", "Always");

        values.put("service.type", "ClusterIP");
        values.put("service.port", "80");
        values.put("service.targetPort", "9001");

        values.put("persistence.storageClass", "csi-cinder-classic");
        values.put("persistence.accessModes[0]", "ReadWriteOnce");
        values.put("persistence.size", "1Gi");

        values.put("env.HOST", "https://" + domain);
        values.put("env.PORT", "9001");
        values.put("env.DB_PATH", "/db");
        values.put("env.CLIENT_SECRET", authKey);
        values.put("env.ADMIN_SECRET", this.adminSecret);
        values.put("env.NAME", issuerDeployment.getOrganization());

        values.put("virtualService.gateway", "default/evdi-gateway");
        values.put("virtualService.hosts[0]", domain);

        values.put("imagePullSecrets[0].name", "harbor-registry-secret");

        // --- Blockchain configuration setup ---
        BlockchainConfiguration bc = issuerDeployment.getBlockchainConfiguration();
        values.put("env.BLOCKCHAIN_RPC_URL", bc.getRpc());
        values.put("env.BLOCKCHAIN_REST_API_URL", bc.getLcd());
        values.put("env.NETWORK", bc.getType().name().toLowerCase());

        return values;
    }

    // Build values map for VerifierDeployment
    private Map<String, String> buildVerifierValuesMap(VerifierDeployment verifierDeployment, String authKey) {
        Map<String, String> values = new HashMap<>();
        String domain = String.format("%s.evdi.app", verifierDeployment.getVerifierName());

        values.put("replicaCount", "1");
        values.put("uniqueName", verifierDeployment.getVerifierName());

        values.put("image.repository", "309596z9.c1.gra9.container-registry.ovh.net/empe/services/verifier-sdk");
        values.put("image.tag", verifierDeployment.getVerifierVersion().getVersion());
        values.put("image.pullPolicy", "Always");

        values.put("service.type", "ClusterIP");
        values.put("service.port", "80");
        values.put("service.targetPort", "9001");

        values.put("persistence.storageClass", "csi-cinder-classic");
        values.put("persistence.accessModes[0]", "ReadWriteOnce");
        values.put("persistence.size", "1Gi");

        values.put("env.HOST", "https://" + domain);
        values.put("env.PORT", "9001");
        values.put("env.DB_PATH", "/db");
        values.put("env.CLIENT_SECRET", authKey);

        values.put("virtualService.gateway", "default/evdi-gateway");
        values.put("virtualService.hosts[0]", domain);

        values.put("imagePullSecrets[0].name", "harbor-registry-secret");

        // --- Blockchain configuration setup ---
        BlockchainConfiguration bc = verifierDeployment.getBlockchainConfiguration();
        values.put("env.BLOCKCHAIN_RPC_URL", bc.getRpc());
        values.put("env.BLOCKCHAIN_REST_API_URL.lcd", bc.getLcd());
        values.put("env.NETWORK", bc.getType().name().toLowerCase());


        return values;
    }

    // Method to perform Helm upgrade for Issuer with specified image version
    private Release upgradeIssuerClientSecret(String issuerName, String authKey) throws Exception {
        // Path to the Helm chart
        URL resource = getClass().getClassLoader().getResource("issuer-sdk-helm");
        if (resource == null) {
            throw new IllegalArgumentException("Helm chart path not found in resources");
        }
        Path helmChartPath = Paths.get(resource.toURI());
        logger.info("Starting client secret upgrade for issuer: {} with chart path: {}", issuerName, helmChartPath);

        // Create the upgrade command for the Helm chart
        UpgradeCommand upgradeCommand = new Helm(helmChartPath).upgrade();

        logger.debug("Initializing Helm upgrade command for issuer: {}", issuerName);

        // Configure the upgrade command
        Release release = upgradeCommand
                .withName(issuerName)
                .withNamespace(namespace)
                .reuseValues()
                .set("env.CLIENT_SECRET", authKey)
                .withDescription("Change issuer client secret")
                .withKubeConfig(Paths.get(kubeConfigPath))
                .debug()
                .call();

        logger.info("Helm upgrade command successfully executed for issuer: {}", issuerName);
        return release;
    }

    // Method to perform Helm upgrade for Verifier with specified client secret
    private Release upgradeVerifierClientSecret(String verifierName, String authKey) throws Exception {
        URL resource = getClass().getClassLoader().getResource("verifier-sdk-helm");
        if (resource == null) {
            throw new IllegalArgumentException("Helm chart path not found in resources");
        }
        Path helmChartPath = Paths.get(resource.toURI());

        logger.info("Starting client secret upgrade for verifier: {} with chart path: {}", verifierName, helmChartPath);

        // Create the upgrade command for the Helm chart
        UpgradeCommand upgradeCommand = new Helm(helmChartPath).upgrade();

        logger.debug("Initializing Helm upgrade command for verifier: {}", verifierName);

        // Configure the upgrade command
        Release release = upgradeCommand
                .withName(verifierName)
                .withNamespace(namespace)
                .reuseValues()
                .set("env.CLIENT_SECRET", authKey)
                .withDescription("Change verifier client secret")
                .withKubeConfig(Paths.get(kubeConfigPath))
                .debug()
                .call();

        logger.info("Helm upgrade command successfully executed for verifier: {}", verifierName);
        return release;
    }

    // Method to schedule an upgrade for Issuer client secret
    public void scheduleClientSecretUpgrade(IssuerDeployment issuerDeployment, String authKey) {
        logger.info("Scheduling client secret upgrade for issuer: {}", issuerDeployment.getIssuerName());
        issuerDeployment.setStatus(Status.UPGRADE);
        issuerDeploymentRepository.save(issuerDeployment);

        try {
            // Perform the client secret upgrade using Helm
            logger.debug("Starting client secret upgrade process for issuer: {}", issuerDeployment.getIssuerName());
            Release result = upgradeIssuerClientSecret(
                    issuerDeployment.getIssuerName(),
                    authKey
            );
            logger.info("Helm upgrade result: {}", result);
            // Mark the deployment as active
            issuerDeployment.setStatus(Status.ACTIVE);
            issuerDeploymentRepository.save(issuerDeployment);
        } catch (Exception e) {
            logger.error("Failed to upgrade client secret for issuer: {}. Error: {}",
                    issuerDeployment.getIssuerName(), e.getMessage(), e);
            issuerDeployment.setStatus(Status.FAILED);
            issuerDeploymentRepository.save(issuerDeployment);
        }
    }

    // Method to schedule an upgrade for Verifier client secret
    @Override
    public void scheduleClientSecretUpgrade(VerifierDeployment verifierDeployment, String authKey) {
        logger.info("Scheduling client secret upgrade for verifier: {}", verifierDeployment.getVerifierName());
        verifierDeployment.setStatus(Status.UPGRADE);
        verifierDeploymentRepository.save(verifierDeployment);

        try {
            // Perform the client secret upgrade using Helm (or another tool for Verifier)
            logger.debug("Starting client secret upgrade process for verifier: {}", verifierDeployment.getVerifierName());
            Release result = upgradeVerifierClientSecret(
                    verifierDeployment.getVerifierName(),
                    authKey
            );
            logger.info("Helm upgrade result: {}", result);
            // Mark the deployment as active
            verifierDeployment.setStatus(Status.ACTIVE);
            verifierDeploymentRepository.save(verifierDeployment);
        } catch (Exception e) {
            logger.error("Failed to upgrade client secret for verifier: {}. Error: {}",
                    verifierDeployment.getVerifierName(), e.getMessage(), e);
            verifierDeployment.setStatus(Status.FAILED);
            verifierDeploymentRepository.save(verifierDeployment);
        }
    }

    @Override
    public void scheduleUpgrade(IssuerDeployment deployment) {
        logger.info("Scheduling upgrade for issuer: {}", deployment.getIssuerName());
        deployment.setStatus(Status.UPGRADE);
        issuerDeploymentRepository.save(deployment);

        try {
            // Perform the upgrade
            Release result = upgradeIssuerImage(deployment.getIssuerName(), deployment.getIssuerVersion().getVersion());
            logger.info("Helm upgrade result: {}", result);

            // Mark the deployment as active after successful upgrade
            deployment.setStatus(Status.ACTIVE);
            issuerDeploymentRepository.save(deployment);
        } catch (Exception e) {
            logger.error("Error upgrading issuer: {}. Error: {}", deployment.getIssuerName(), e.getMessage(), e);
            deployment.setStatus(Status.FAILED);
            deployment.setFailReason("Upgrade failed: " + e.getMessage());
            issuerDeploymentRepository.save(deployment);
        }
    }

    @Override
    public void scheduleUpgrade(VerifierDeployment deployment) {
        logger.info("Scheduling upgrade for verifier: {}", deployment.getVerifierName());
        deployment.setStatus(Status.UPGRADE);
        verifierDeploymentRepository.save(deployment);

        try {
            // Perform the upgrade
            Release result = upgradeVerifierImage(deployment.getVerifierName(), deployment.getVerifierVersion().getVersion());
            logger.info("Helm upgrade result: {}", result);

            // Mark the deployment as active after successful upgrade
            deployment.setStatus(Status.ACTIVE);
            verifierDeploymentRepository.save(deployment);
        } catch (Exception e) {
            logger.error("Error upgrading verifier: {}. Error: {}", deployment.getVerifierName(), e.getMessage(), e);
            deployment.setStatus(Status.FAILED);
            deployment.setFailReason("Upgrade failed: " + e.getMessage());
            verifierDeploymentRepository.save(deployment);
        }
    }


    // Method to perform Helm upgrade for Issuer with specified image version
    private Release upgradeIssuerImage(String issuerName, String imageVersion) throws Exception {
        // Path to the Helm chart
        URL resource = getClass().getClassLoader().getResource("issuer-sdk-helm");
        if (resource == null) {
            throw new IllegalArgumentException("Helm chart path not found in resources");
        }
        Path helmChartPath = Paths.get(resource.toURI());

        // Create the upgrade command for Helm chart
        UpgradeCommand upgradeCommand = new Helm(helmChartPath).upgrade();

        // Set the release name, namespace, and the image version to upgrade to
        return upgradeCommand
                .withName(issuerName)
                .withNamespace(namespace)
                .reuseValues()
                .set("image.tag", imageVersion) // Set the image version to upgrade to
                .withDescription("Upgrade " + issuerName + " to image version " + imageVersion) // Custom description (optional)
                .withKubeConfig(Paths.get(kubeConfigPath))
                .debug()
                .call();
    }

    private Release upgradeVerifierImage(String issuerName, String imageVersion) throws Exception {
        // Path to the Helm chart
        URL resource = getClass().getClassLoader().getResource("verifier-sdk-helm");
        if (resource == null) {
            throw new IllegalArgumentException("Helm chart path not found in resources");
        }
        Path helmChartPath = Paths.get(resource.toURI());

        // Create the upgrade command for Helm chart
        UpgradeCommand upgradeCommand = new Helm(helmChartPath).upgrade();

        // Set the release name, namespace, and the image version to upgrade to
        return upgradeCommand
                .withName(issuerName)
                .withNamespace(namespace)
                .reuseValues()
                .set("image.tag", imageVersion) // Set the image version to upgrade to
                .withDescription("Upgrade " + issuerName + " to image version " + imageVersion) // Custom description (optional)
                .withKubeConfig(Paths.get(kubeConfigPath))
                .debug()
                .call();
    }

    private void addPostgreSQLCredentials(Map<String, String> values, PostgreSQLCredentials credentials) {
        values.put("env.DB_HOST", credentials.getHost());
        values.put("env.DB_PORT", credentials.getPort());
        values.put("env.DB_USERNAME", credentials.getUsername());
        values.put("env.DB_PASSWORD", credentials.getPassword());
        values.put("env.DB_DATABASE", credentials.getDatabaseName());
    }

    private void addVaultCredentials(Map<String, String> values, VaultCredentials credentials) {
        values.put("env.VAULT_ADDR", credentials.getVaultAddr());
        values.put("env.VAULT_USERNAME", credentials.getUsername());
        values.put("env.VAULT_PASSWORD", credentials.getPassword());
    }

}
