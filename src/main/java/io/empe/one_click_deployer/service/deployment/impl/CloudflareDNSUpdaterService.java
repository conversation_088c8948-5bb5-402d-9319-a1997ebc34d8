package io.empe.one_click_deployer.service.deployment.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.empe.one_click_deployer.dto.DNSRecordCloudflare;
import io.empe.one_click_deployer.exception.CloudflareException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

@Service
@Slf4j
public class CloudflareDNSUpdaterService {

    private static final String API_URL = "https://api.cloudflare.com/client/v4/zones/f13dafe77a8f55774f5025316eda6668/dns_records";

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    @Value("${cloudflare.authEmail}")
    private String authEmail;

    @Value("${cloudflare.apiToken}")
    private String apiToken;

    public CloudflareDNSUpdaterService(RestTemplate restTemplate, ObjectMapper objectMapper) {
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
    }

    /**
     * Adds a DNS record to Cloudflare.
     *
     * @param type    The type of DNS record (e.g., "A", "CNAME").
     * @param name    The name of the DNS record (e.g., "example.com").
     * @param content The content of the DNS record (e.g., IP address or another domain).
     * @param ttl     The time-to-live for the DNS record in seconds.
     * @param proxied Whether the record is proxied through Cloudflare.
     */
    public void addDNSRecord(String type, String name, String content, int ttl, boolean proxied) throws CloudflareException {
        DNSRecordCloudflare dnsRecord = new DNSRecordCloudflare(type, name, content, ttl, proxied);

        HttpHeaders headers = new HttpHeaders();
        headers.set("X-Auth-Email", authEmail);
        headers.set("Authorization", "Bearer " + apiToken);
        headers.set("Content-Type", "application/json");

        HttpEntity<DNSRecordCloudflare> requestEntity = new HttpEntity<>(dnsRecord, headers);

        try {
            ResponseEntity<String> response = restTemplate.exchange(
                    API_URL, HttpMethod.POST, requestEntity, String.class
            );

            if (!response.getStatusCode().is2xxSuccessful()) {
                log.error("Failed to add DNS record. Status code: {}, Body: {}", response.getStatusCode(), response.getBody());
                throw new CloudflareException("Failed to add DNS record. Status code: " + response.getStatusCode() +
                        ", Body: " + response.getBody());
            }

            log.info("DNS Record Added Successfully: {}", response.getBody());
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            log.error("HTTP error occurred while adding DNS record. Status code: {}, Body: {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new CloudflareException("HTTP error occurred while adding DNS record. Status code: " + e.getStatusCode() +
                    ", Body: " + e.getResponseBodyAsString());
        } catch (RestClientException e) {
            log.error("Error occurred while communicating with Cloudflare API. {}", e.getMessage());
            throw new CloudflareException("Error occurred while communicating with Cloudflare API." + e.getMessage());
        } catch (CloudflareException e) {
            log.error("CloudflareException: {}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    /**
     * Deletes a DNS record by its name.
     *
     * @param recordName The name of the DNS record to delete.
     * @throws CloudflareException if the operation fails.
     */
    public void deleteDNSRecordByName(String recordName) throws CloudflareException {
        HttpHeaders headers = new HttpHeaders();
        headers.set("X-Auth-Email", authEmail);
        headers.set("Authorization", "Bearer " + apiToken);
        headers.set("Content-Type", "application/json");

        try {
            // Step 1: Get DNS record ID by name
            ResponseEntity<String> response = restTemplate.exchange(
                    API_URL + "?name=" + recordName, HttpMethod.GET, new HttpEntity<>(headers), String.class
            );

            if (!response.getStatusCode().is2xxSuccessful()) {
                log.error("Failed to retrieve DNS record. Status code: {}", response.getStatusCode());
                throw new CloudflareException("Failed to retrieve DNS record. Status code: " + response.getStatusCode());
            }

            String recordId = extractRecordId(response.getBody(), recordName);
            if (recordId == null) {
                log.warn("DNS record with name '{}' not found.", recordName);
                throw new CloudflareException("DNS record with name '" + recordName + "' not found.");
            }

            // Step 2: Delete DNS record by ID
            ResponseEntity<String> deleteResponse = restTemplate.exchange(
                    API_URL + "/" + recordId, HttpMethod.DELETE, new HttpEntity<>(headers), String.class
            );

            if (!deleteResponse.getStatusCode().is2xxSuccessful()) {
                log.error("Failed to delete DNS record. Status code: {}, Body: {}", deleteResponse.getStatusCode(), deleteResponse.getBody());
                throw new CloudflareException("Failed to delete DNS record. Status code: " + deleteResponse.getStatusCode() +
                        ", Body: " + deleteResponse.getBody());
            }

            log.info("DNS Record Deleted Successfully: {}", deleteResponse.getBody());
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            log.error("HTTP error occurred while deleting DNS record. Status code: {}, Body: {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new CloudflareException("HTTP error occurred while deleting DNS record. Status code: " + e.getStatusCode() +
                    ", Body: " + e.getResponseBodyAsString());
        } catch (RestClientException e) {
            log.error("Error occurred while communicating with Cloudflare API. {}", e.getMessage());
            throw new CloudflareException("Error occurred while communicating with Cloudflare API. " + e.getMessage());
        }
    }

    /**
     * Extracts the record ID from the API response.
     *
     * @param responseBody The JSON response from the Cloudflare API.
     * @param recordName   The name of the DNS record to find.
     * @return The record ID if found, otherwise null.
     * @throws CloudflareException if JSON parsing fails.
     */
    private String extractRecordId(String responseBody, String recordName) throws CloudflareException {
        try {
            JsonNode root = objectMapper.readTree(responseBody);
            JsonNode result = root.path("result");

            for (JsonNode record : result) {
                if (record.path("name").asText().equals(recordName)) {
                    return record.path("id").asText();
                }
            }
        } catch (Exception e) {
            log.error("Error parsing JSON response: {}", e.getMessage());
            throw new CloudflareException("Error parsing JSON response: " + e.getMessage());
        }

        return null;
    }
}