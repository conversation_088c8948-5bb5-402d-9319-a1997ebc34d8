package io.empe.one_click_deployer.exception;

import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(enumAsRef = true)
public enum UserErrorCode {
    GENERAL_ERROR(5000, "An error occurred"),
    INVALID_USER_PARAMS(4001, "Invalid user parameters"),
    USER_ALREADY_EXISTS(4002, "User already exists"),
    UNAUTHORIZED(4003, "Authentication failed"),
    ISSUER_NAME_ALREADY_EXISTS(4005, "Issuer name already exists"),
    VERIFIER_NAME_ALREADY_EXISTS(4006, "Verifier name already exists"),
    PERMISSION_DENIED(4007, "Permission denied"),
    REFRESH_TOKEN_NOT_EXIST_OR_EXPIRED(4008, "Refresh token does not exist or has expired"),
    VERSION_NOT_EXIST(4009, "Version not exist"),
    NOT_ENOUGH_DEPLOYMENTS_LEFT(4019, "Not enough deployments left"),

    GRAYLOG_ENCODE_EXCEPTION(4004, "Error encoding Graylog URL"),

    ISSUER_VERSION_NOT_FOUND(4010, "Issuer version not found"),
    ISSUER_VERSION_ALREADY_EXISTS(4011, "Issuer version already exists"),
    ISSUER_VERSION_UPDATE_FAILED(4012, "Failed to update issuer version"),
    ISSUER_VERSION_DELETE_FAILED(4013, "Failed to delete issuer version"),

    VERIFIER_VERSION_NOT_FOUND(4014, "Verifier version not found"),
    VERIFIER_VERSION_ALREADY_EXISTS(4015, "Verifier version already exists"),
    VERIFIER_VERSION_UPDATE_FAILED(4016, "Failed to update verifier version"),
    VERIFIER_VERSION_DELETE_FAILED(4017, "Failed to delete verifier version"),

    USER_EMAIL_NOT_CONFIRMED(4018, "User email not confirmed"),
    INVALID_TOKEN(4019, "Invalid token"),
    MAIL_SEND_ERROR(4020, "Mail sending failed error"),

    BACKUP_OPERATION_FAILED(4021, "Backup operation failed"),

    SET_ISSUER_LIMITS_ERROR(4022, "Error setting issuer limits");

    @JsonValue
    private final int code;
    private final String description;

    UserErrorCode(int code, String description) {
        this.code = code;
        this.description = description;
    }
}
