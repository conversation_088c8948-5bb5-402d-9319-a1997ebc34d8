package io.empe.one_click_deployer.exception.external;

import io.empe.one_click_deployer.exception.RestExceptionBase;
import io.empe.one_click_deployer.exception.UserErrorCode;
import org.springframework.http.HttpStatus;

public class MailSendError extends RestExceptionBase {
    public MailSendError(String message) {
        super(UserErrorCode.MAIL_SEND_ERROR.getCode(), message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
}
