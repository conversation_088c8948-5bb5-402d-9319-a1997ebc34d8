package io.empe.one_click_deployer.exception.external;

import io.empe.one_click_deployer.exception.RestExceptionBase;
import io.empe.one_click_deployer.exception.UserErrorCode;
import org.springframework.http.HttpStatus;

public class NotEnoughDeploymentLeft extends RestExceptionBase {
    public NotEnoughDeploymentLeft(String customMessage) {
        super(UserErrorCode.NOT_ENOUGH_DEPLOYMENTS_LEFT.getCode(), customMessage, HttpStatus.BAD_REQUEST);
    }
}
