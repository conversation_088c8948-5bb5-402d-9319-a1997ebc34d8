package io.empe.one_click_deployer.exception.external;

import io.empe.one_click_deployer.exception.RestExceptionBase;
import io.empe.one_click_deployer.exception.UserErrorCode;
import org.springframework.http.HttpStatus;

public class InvalidVersionUpgradeException extends RestExceptionBase {
    public InvalidVersionUpgradeException(String message) {
        super(UserErrorCode.INVALID_USER_PARAMS.getCode(), message, HttpStatus.BAD_REQUEST);
    }
}
