package io.empe.one_click_deployer.exception.external;

import io.empe.one_click_deployer.exception.RestExceptionBase;
import io.empe.one_click_deployer.exception.UserErrorCode;
import org.springframework.http.HttpStatus;

public class RefreshTokenNotExistOrExpiredException extends RestExceptionBase {

    public RefreshTokenNotExistOrExpiredException(String customMessage) {
        super(UserErrorCode.REFRESH_TOKEN_NOT_EXIST_OR_EXPIRED.getCode(), customMessage, HttpStatus.UNAUTHORIZED);
    }
}
