package io.empe.one_click_deployer.exception.external;

import io.empe.one_click_deployer.exception.RestExceptionBase;
import io.empe.one_click_deployer.exception.UserErrorCode;
import org.springframework.http.HttpStatus;

public class UserEmailNotConfirmed extends RestExceptionBase {
    public UserEmailNotConfirmed(String message) {
        super(UserErrorCode.USER_EMAIL_NOT_CONFIRMED.getCode(), message, HttpStatus.BAD_REQUEST);
    }
}
