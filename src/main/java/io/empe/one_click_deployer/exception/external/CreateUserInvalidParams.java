package io.empe.one_click_deployer.exception.external;

import io.empe.one_click_deployer.exception.RestExceptionBase;
import io.empe.one_click_deployer.exception.UserErrorCode;
import org.springframework.http.HttpStatus;

public class CreateUserInvalidParams extends RestExceptionBase {
    public CreateUserInvalidParams(String customMessage) {
        super(UserErrorCode.INVALID_USER_PARAMS.getCode(), customMessage, HttpStatus.BAD_REQUEST);
    }
}
