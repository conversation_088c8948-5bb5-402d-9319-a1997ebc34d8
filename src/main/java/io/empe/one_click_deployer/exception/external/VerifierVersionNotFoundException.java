package io.empe.one_click_deployer.exception.external;

import io.empe.one_click_deployer.exception.RestExceptionBase;
import io.empe.one_click_deployer.exception.UserErrorCode;
import org.springframework.http.HttpStatus;

public class VerifierVersionNotFoundException extends RestExceptionBase {
    public VerifierVersionNotFoundException(String customMessage) {
        super(UserErrorCode.VERIFIER_VERSION_NOT_FOUND.getCode(), customMessage, HttpStatus.NOT_FOUND);
    }
}
