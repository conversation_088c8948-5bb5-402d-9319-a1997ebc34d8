package io.empe.one_click_deployer.exception.external;

import io.empe.one_click_deployer.exception.RestExceptionBase;
import io.empe.one_click_deployer.exception.UserErrorCode;
import org.springframework.http.HttpStatus;

public class IssuerNameAlreadyExistsExceptionBase extends RestExceptionBase {
    public IssuerNameAlreadyExistsExceptionBase(String customMessage) {
        super(UserErrorCode.ISSUER_NAME_ALREADY_EXISTS.getCode(), customMessage, HttpStatus.CONFLICT);
    }
}
