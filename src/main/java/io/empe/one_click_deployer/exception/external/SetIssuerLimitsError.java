package io.empe.one_click_deployer.exception.external;

import io.empe.one_click_deployer.exception.RestExceptionBase;
import io.empe.one_click_deployer.exception.UserErrorCode;
import org.springframework.http.HttpStatus;

public class SetIssuerLimitsError extends RestExceptionBase {
    public SetIssuerLimitsError(String message) {
        super(UserErrorCode.SET_ISSUER_LIMITS_ERROR.getCode(), message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
}
