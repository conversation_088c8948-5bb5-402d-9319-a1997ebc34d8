package io.empe.one_click_deployer.exception.external;

import io.empe.one_click_deployer.exception.RestExceptionBase;
import io.empe.one_click_deployer.exception.UserErrorCode;
import org.springframework.http.HttpStatus;

public class VersionNotExistsExceptionBase extends RestExceptionBase {
    public VersionNotExistsExceptionBase(String customMessage) {
        super(UserErrorCode.VERSION_NOT_EXIST.getCode(), customMessage, HttpStatus.BAD_REQUEST);
    }
}
