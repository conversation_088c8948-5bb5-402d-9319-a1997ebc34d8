package io.empe.one_click_deployer.exception.external;

import io.empe.one_click_deployer.exception.RestExceptionBase;
import io.empe.one_click_deployer.exception.UserErrorCode;
import org.springframework.http.HttpStatus;

public class InvalidToken extends RestExceptionBase {
    public InvalidToken(String message) {
        super(UserErrorCode.INVALID_TOKEN.getCode(), message, HttpStatus.BAD_REQUEST);
    }
}
