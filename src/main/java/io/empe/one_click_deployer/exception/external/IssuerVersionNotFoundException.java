package io.empe.one_click_deployer.exception.external;

import io.empe.one_click_deployer.exception.RestExceptionBase;
import io.empe.one_click_deployer.exception.UserErrorCode;
import org.springframework.http.HttpStatus;

public class IssuerVersionNotFoundException extends RestExceptionBase {
    public IssuerVersionNotFoundException(String customMessage) {
        super(UserErrorCode.ISSUER_VERSION_NOT_FOUND.getCode(), customMessage, HttpStatus.NOT_FOUND);
    }
}
