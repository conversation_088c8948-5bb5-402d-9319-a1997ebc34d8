package io.empe.one_click_deployer.exception.external;

import io.empe.one_click_deployer.exception.RestExceptionBase;
import io.empe.one_click_deployer.exception.UserErrorCode;
import org.springframework.http.HttpStatus;

public class PermissionDeniedException extends RestExceptionBase {
    public PermissionDeniedException(String customMessage) {
        super(UserErrorCode.PERMISSION_DENIED.getCode(), customMessage, HttpStatus.FORBIDDEN);
    }
}
