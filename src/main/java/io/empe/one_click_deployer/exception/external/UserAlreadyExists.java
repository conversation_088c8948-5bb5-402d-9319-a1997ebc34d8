package io.empe.one_click_deployer.exception.external;

import io.empe.one_click_deployer.exception.RestExceptionBase;
import io.empe.one_click_deployer.exception.UserErrorCode;
import org.springframework.http.HttpStatus;

public class UserAlreadyExists extends RestExceptionBase {
    public UserAlreadyExists(String message) {
        super(UserErrorCode.USER_ALREADY_EXISTS.getCode(), message, HttpStatus.BAD_REQUEST);
    }
}
