package io.empe.one_click_deployer.exception.external;

import io.empe.one_click_deployer.exception.RestExceptionBase;
import io.empe.one_click_deployer.exception.UserErrorCode;
import org.springframework.http.HttpStatus;

public class GraylogEncodeException extends RestExceptionBase {
    public GraylogEncodeException(String message) {
        super(UserErrorCode.GRAYLOG_ENCODE_EXCEPTION.getCode(), message, HttpStatus.BAD_REQUEST);
    }
}
