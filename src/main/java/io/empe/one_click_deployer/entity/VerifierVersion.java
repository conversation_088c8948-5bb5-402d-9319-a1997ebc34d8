package io.empe.one_click_deployer.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "verifier_version")
public class VerifierVersion {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column()
    private String label;

    @Column(nullable = false)
    private String version;

    @Column(name = "chart_version")
    private String chartVersion;
}

