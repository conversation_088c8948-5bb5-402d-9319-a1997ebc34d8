package io.empe.one_click_deployer.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "issuer_version")
public class IssuerVersion {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column()
    private String label;  // Label for the version (e.g. "stable", "lts")

    @Column(nullable = false)
    private String version;  // The version of the application (e.g. "v1.0.0")

    @Column(name = "chart_version")
    private String chartVersion;  // Optional: The version of the chart (e.g. "v1.0.0-helm")
}
