package io.empe.one_click_deployer.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "verifier_deployment")
public class VerifierDeployment {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Column(nullable = false, unique = true)
    private String verifierName;

    @Column(nullable = false)
    private String verifierReadableName;

    @Column(nullable = false)
    private String fullHost;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Status status;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private DeploymentType deploymentType;

    @Column(length = 1024)
    private String failReason;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "verifier_version_id", nullable = false)
    private VerifierVersion verifierVersion;  // The version details of the issuer

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "blockchain_configuration_id", nullable = false)
    private BlockchainConfiguration blockchainConfiguration;
}
