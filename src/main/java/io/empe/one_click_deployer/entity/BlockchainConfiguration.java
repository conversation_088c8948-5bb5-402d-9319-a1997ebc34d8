package io.empe.one_click_deployer.entity;


import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
public class BlockchainConfiguration {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String rpc;
    private String lcd;

    @Column(name = "faucet_url")
    private String faucetUrl;

    @Enumerated(EnumType.STRING)
    private BlockchainType type;
}
