package io.empe.one_click_deployer.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "issuer_deployment")
public class IssuerDeployment {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Column(nullable = false, unique = true)
    private String issuerName;

    @Column(nullable = false)
    private String issuerReadableName;

    @Column(nullable = false)
    private String fullHost;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Status status;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private DeploymentType deploymentType;

    @Column(length = 1024)
    private String failReason;

    @Column(nullable = false)
    private String organization;

    @Column()
    private String issuerCreatorAddress;

    @Column()
    private String didDocument;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "issuer_version_id", nullable = false)
    private IssuerVersion issuerVersion;  // The version details of the issuer

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "blockchain_configuration_id", nullable = false)
    private BlockchainConfiguration blockchainConfiguration;
}
