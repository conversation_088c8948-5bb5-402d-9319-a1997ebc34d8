package io.empe.one_click_deployer.entity;

import jakarta.persistence.*;
import jdk.jfr.Timestamp;
import lombok.*;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "user_subscription")
public class UserSubscription {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * The Stripe (or other) product identifier for this subscription
     */
    @Column(name = "product_id", nullable = false)
    private String productId;

    /**
     * When this subscription record was created
     */
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    /**
     * End of the current billing period for this subscription.
     */
    @Timestamp
    @Column(name = "current_period_end", nullable = false)
    private LocalDateTime currentPeriodEnd;

    /**
     * <PERSON>e’s subscription instance ID
     */
    @Column(name = "stripe_subscription_id")
    private String stripeSubscriptionId;

    /**
     * Is the subscription currently active?
     */
    @Column(name = "subscription_active", nullable = false)
    private boolean subscriptionActive = false;

    @Column(name = "stripe_customer")
    private String stripeCustomer;

    /**
     * Owning user
     */
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_id", nullable = false, unique = true)
    private User user;

    @OneToMany(mappedBy = "userSubscription",
            cascade = CascadeType.ALL,
            orphanRemoval = true,
            fetch = FetchType.LAZY)
    private Set<Metadata> metadata = new HashSet<>();
}