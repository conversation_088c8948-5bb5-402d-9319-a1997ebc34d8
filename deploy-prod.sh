#!/usr/bin/env bash
set -euo pipefail

POM_FILE="pom.xml"
YAML_FILE="deployment-prod/one-click-deployer-deployment.yaml"
IMAGE_REPO="309596z9.c1.gra9.container-registry.ovh.net/empe/services/one-click-deployer"

current_ver=$(grep -oE '<version>[0-9]+\.[0-9]+\.[0-9]+' "$POM_FILE" | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' | head -n 2 | tail -n 1)
IFS='.' read -r major minor patch <<<"$current_ver"
new_patch=$((patch + 1))
new_ver="${major}.${minor}.${new_patch}"

echo "Version: ${current_ver} -> ${new_ver}"

if [[ "$(uname)" == "Darwin" ]]; then
  sed -i '' "/<artifactId>one-click-deployer<\/artifactId>/{n; s|<version>[0-9.]*</version>|<version>${new_ver}</version>|;}" "$POM_FILE"
  sed -i '' "s|${IMAGE_REPO}:${current_ver}|${IMAGE_REPO}:${new_ver}|g" "$YAML_FILE"
else
  sed -i "/<artifactId>one-click-deployer<\/artifactId>/{n; s|<version>[0-9.]*</version>|<version>${new_ver}</version>|;}" "$POM_FILE"
  sed -i "s|${IMAGE_REPO}:${current_ver}|${IMAGE_REPO}:${new_ver}|g" "$YAML_FILE"
fi

echo "Building Docker image..."
docker build --platform linux/amd64 -t "${IMAGE_REPO}:${new_ver}" .

echo "Pushing Docker image..."
docker push "${IMAGE_REPO}:${new_ver}"

echo "Applying Kubernetes manifests..."
kubectl apply -f deployment-prod

echo "Done. Deployed version ${new_ver}"